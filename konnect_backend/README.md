# Konnect Backend

This is the backend server for the Konnect mobile application, providing API endpoints for timetable management.

## Setup

### Prerequisites

- Go 1.23 or higher
- PostgreSQL database

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
DATABASE_URL=postgresql://konnectdb_owner:<EMAIL>/konnectdb?sslmode=require
REDIS_URL=localhost:6379
REDIS_PASSWORD=
PORT=8080
```

### Running Locally

1. Clone the repository
2. Navigate to the project directory
3. Run the database migrations:
   ```
   psql -U <username> -d <database> -f db/migrations/001_create_timetable.sql
   ```
4. Build and run the server:
   ```
   go build -o konnect-server ./cmd/server
  
