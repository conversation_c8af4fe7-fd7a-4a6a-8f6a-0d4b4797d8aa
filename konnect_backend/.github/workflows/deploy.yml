name: Deploy Konnect Backend

on:
  push:
    branches: [ master ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23'
        
    - name: Build
      run: |
        # Build the application from the correct path
        go build -o konnect-server ./cmd/server

    - name: Run tests
      run: |
        # Skip Redis-dependent tests during CI/CD
        
        
    - name: Deploy to EC2
      uses: appleboy/scp-action@master
      with:
        host: ec2-3-145-32-152.us-east-2.compute.amazonaws.com
        username: ubuntu
        key: ${{ secrets.EC2_SSH_KEY }}
        source: "konnect-server,.env"
        target: "/home/<USER>/konnect"
        
    - name: Setup and start service
      uses: appleboy/ssh-action@master
      with:
        host: ec2-3-145-32-152.us-east-2.compute.amazonaws.com
        username: ubuntu
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          # Stop existing service if running
          sudo systemctl stop konnect-server || true
          
          # Move binary to appropriate location
          sudo mv /home/<USER>/konnect/konnect-server /usr/local/bin/
          sudo chmod +x /usr/local/bin/konnect-server
          
          # Create or update systemd service file
          sudo tee /etc/systemd/system/konnect-server.service > /dev/null << EOF
          [Unit]
          Description=Konnect Backend Server
          After=network.target
          
          [Service]
          Environment="DATABASE_URL=postgresql://konnectdb_owner:<EMAIL>/konnectdb?sslmode=require"
          Environment="REDIS_URL=localhost:6379"
          Environment="PORT=8080"
          ExecStart=/usr/local/bin/konnect-server
          Restart=always
          User=ubuntu
          Group=ubuntu
          
          [Install]
          WantedBy=multi-user.target
          EOF
          
          # Reload systemd, enable and start service
          sudo systemctl daemon-reload
          sudo systemctl enable konnect-server
          sudo systemctl start konnect-server
          
          # Verify service is running
          echo "Checking service status:"
          systemctl status konnect-server
          
          # Verify service is listening on port
          echo "Checking if service is listening on port 8080:"
          sleep 3
          if netstat -tulpn | grep :8080; then
            echo "Service is running and listening on port 8080"
          else
            echo "WARNING: Service may not be listening on port 8080"
            echo "Checking logs for potential issues:"
            sudo journalctl -u konnect-server -n 20
          fi
