# 1. First request - should come from database
curl http://localhost:8080/api/timetable

# 2. Second request - should come from cache
curl http://localhost:8080/api/timetable

# 3. Get classes for a specific date (first time - from DB)
curl http://localhost:8080/api/timetable/2023-11-20

# 4. Get classes for the same date again (should be from cache)
curl http://localhost:8080/api/timetable/2023-11-20

# 5. Add a new class (will invalidate cache)
curl -X POST http://localhost:8080/api/timetable \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Class",
    "code": "TEST101",
    "day": "Monday",
    "start_time": "10:00",
    "end_time": "11:30",
    "location": "Room 123",
    "professor": "Dr. Test",
    "reminders": false
  }'

# 6. Get all classes again (should be from DB since cache was invalidated)
curl http://localhost:8080/api/timetable

# 7. Get all classes one more time (should be from cache now)
curl http://localhost:8080/api/timetable

# 8. Update a class (replace ID with an actual ID from your database)
curl -X PUT http://localhost:8080/api/timetable/1 \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Class",
    "code": "TEST101",
    "day": "Monday",
    "start_time": "10:00",
    "end_time": "11:30",
    "location": "Room 456",
    "professor": "Dr. Update",
    "reminders": true
  }'

# 9. Get all classes again (should be from DB since cache was invalidated)
curl http://localhost:8080/api/timetable

# 10. Delete a class (replace ID with an actual ID from your database)
curl -X DELETE http://localhost:8080/api/timetable/1

# 11. Get all classes again (should be from DB since cache was invalidated)
curl http://localhost:8080/api/timetable