package main

import (
	"konnect_backend/internal/cache"
	"konnect_backend/internal/db"
	"konnect_backend/internal/handlers"
	"log"
	"net/http"
	"os"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {
	// Initialize database
	if err := db.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Initialize Redis cache
	if err := cache.Initialize(); err != nil {
		log.Printf("Warning: Failed to initialize Redis cache: %v", err)
		log.Println("Continuing without Redis caching")
	} else {
		defer cache.Close()
	}

	// Create Echo instance
	e := echo.New()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	// Add trailing slash middleware to handle both with and without trailing slash
	e.Pre(middleware.RemoveTrailingSlash())

	// Routes
	e.GET("/", func(c echo.Context) error {
		return c.String(http.StatusOK, "Konnect API is running")
	})

	// API routes
	api := e.Group("/api")

	// Timetable routes
	timetableHandler := handlers.NewTimetableHandler()
	api.GET("/timetable", timetableHandler.GetAllClasses)
	api.GET("/timetable/:date", timetableHandler.GetClassesByDate)
	api.POST("/timetable", timetableHandler.AddClass)
	api.PUT("/timetable/:id", timetableHandler.UpdateClass)
	api.DELETE("/timetable/:id", timetableHandler.DeleteClass)

	// Event routes
	eventHandler := handlers.NewEventHandler()
	api.GET("/events", eventHandler.GetAllEvents)
	api.GET("/events/date/:date", eventHandler.GetEventsByDate)
	api.GET("/events/category/:category", eventHandler.GetEventsByCategory)
	api.POST("/events", eventHandler.AddEvent)
	api.PUT("/events/:id", eventHandler.UpdateEvent)
	api.DELETE("/events/:id", eventHandler.DeleteEvent)

	// Housing routes
	housingHandler := handlers.NewHousingHandler()
	housingHandler.RegisterRoutes(e)

	// Initialize the housing table
	if err := housingHandler.InitTable(); err != nil {
		log.Fatalf("Failed to initialize housing table: %v", err)
	}

	// Health check routes
	healthHandler := handlers.NewHealthHandler()
	api.GET("/health/redis", healthHandler.CheckRedis)
	api.POST("/health/redis/clear", healthHandler.ClearRedisCache)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	e.Logger.Fatal(e.Start(":" + port))
}
