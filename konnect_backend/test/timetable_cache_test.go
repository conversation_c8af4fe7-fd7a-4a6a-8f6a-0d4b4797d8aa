package test

import (
	"konnect_backend/internal/cache"
	"konnect_backend/internal/models"
	"testing"
)

func TestTimetableCache(t *testing.T) {
	// Initialize Redis
	err := cache.Initialize()
	if err != nil {
		t.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer cache.Close()

	// Create test data
	testClass := models.Class{
		ID:        999,
		Name:      "Test Class",
		Code:      "TEST101",
		Day:       "Monday",
		StartTime: "10:00",
		EndTime:   "11:00",
		Location:  "Test Room",
		Professor: "Test Professor",
		Reminders: true,
	}

	// Test caching a class by ID
	cache.SetClassByID(testClass)

	// Retrieve from cache
	cachedClass, found := cache.GetClassByID(testClass.ID)
	if !found {
		t.Fatalf("Failed to retrieve class from cache")
	}

	if cachedClass.Name != testClass.Name || cachedClass.Code != testClass.Code {
		t.Errorf("Cached class data doesn't match original: got %+v, want %+v", cachedClass, testClass)
	}

	// Test cache invalidation
	cache.InvalidateClassByID(testClass.ID)
	_, found = cache.GetClassByID(testClass.ID)
	if found {
		t.<PERSON><PERSON><PERSON>("Class should have been removed from cache")
	}
}
