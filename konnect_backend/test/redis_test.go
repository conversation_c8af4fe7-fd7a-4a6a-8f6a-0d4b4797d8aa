package test

import (
    "konnect_backend/internal/cache"
    "testing"
    "time"
)

func TestRedisConnection(t *testing.T) {
    // Initialize Redis
    err := cache.Initialize()
    if err != nil {
        t.Fatalf("Failed to connect to Redis: %v", err)
    }
    defer cache.Close()

    // Test basic set/get operations
    testKey := "test:connection:" + time.Now().Format(time.RFC3339)
    testValue := "test-value"

    // Set a value
    err = cache.Client.Set(cache.Ctx, testKey, testValue, 1*time.Minute).Err()
    if err != nil {
        t.Fatalf("Failed to set value in Redis: %v", err)
    }

    // Get the value
    val, err := cache.Client.Get(cache.Ctx, testKey).Result()
    if err != nil {
        t.Fatalf("Failed to get value from Redis: %v", err)
    }

    if val != testValue {
        t.<PERSON>rf("Expected value %s, got %s", testValue, val)
    }

    // Clean up
    cache.Client.Del(cache.Ctx, testKey)
}