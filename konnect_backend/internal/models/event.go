package models

import (
	"time"
)

// Event represents an event entry
type Event struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Location    string    `json:"location"`
	Date        string    `json:"date"`
	StartTime   string    `json:"startTime"`
	EndTime     string    `json:"endTime"`
	Category    string    `json:"category"`
	Organizer   string    `json:"organizer"`
	IsReminded  bool      `json:"isReminded"`
	IsFavorite  bool      `json:"isFavorite"`
	Attendees   int       `json:"attendees"`
	CreatedAt   time.Time `json:"createdAt,omitempty"`
}
