package models

import "time"

// Class represents a timetable class entry
type Class struct {
    ID        int       `json:"id"`
    Name      string    `json:"name"`
    Code      string    `json:"code"`
    Day       string    `json:"day"`
    StartTime string    `json:"startTime"`
    EndTime   string    `json:"endTime"`
    Location  string    `json:"location"`
    Professor string    `json:"professor"`
    <PERSON>minders bool      `json:"reminders"`
    CreatedAt time.Time `json:"createdAt,omitempty"`
}