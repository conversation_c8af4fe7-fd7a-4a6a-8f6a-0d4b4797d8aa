package models

import (
	"time"
)

// Housing represents a housing listing
type Housing struct {
	ID           int       `json:"id"`
	Name         string    `json:"name"`
	Address      string    `json:"address"`
	Rent         float64   `json:"rent"`
	Bedrooms     int       `json:"bedrooms"`
	Bathrooms    float64   `json:"bathrooms"`
	SquareFeet   int       `json:"squareFeet"`
	Amenities    []string  `json:"amenities"`
	Description  string    `json:"description"`
	Availability string    `json:"availability"`
	ContactName  string    `json:"contactName"`
	ContactEmail string    `json:"contactEmail"`
	ContactPhone string    `json:"contactPhone"`
	Images       []string  `json:"images"`
	IsFurnished  bool      `json:"isFurnished"`
	CreatedAt    time.Time `json:"createdAt,omitempty"`
}

// PaginatedHousingResponse represents a paginated response for housing listings
type PaginatedHousingResponse struct {
	Listings    []Housing `json:"listings"`
	TotalCount  int       `json:"totalCount"`
	PageCount   int       `json:"pageCount"`
	CurrentPage int       `json:"currentPage"`
	PageSize    int       `json:"pageSize"`
}
