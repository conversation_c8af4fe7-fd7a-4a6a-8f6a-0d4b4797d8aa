package handlers

import (
	"encoding/json"
	"konnect_backend/internal/models"
	"konnect_backend/internal/repository"

	"net/http"
	"strconv"
	"time"

	// "konnect_backend/internal/models"
	// "konnect_backend/internal/repository"

	"github.com/labstack/echo/v4"
)

// TimetableHandler handles timetable-related requests
type TimetableHandler struct {
	repo *repository.TimetableRepository
}

// NewTimetableHandler creates a new timetable handler
func NewTimetableHandler() *TimetableHandler {
	return &TimetableHandler{
		repo: repository.NewTimetableRepository(),
	}
}

// GetAllClasses handles GET /api/timetable
func (h *TimetableHandler) GetAllClasses(c echo.Context) error {
	classes, err := h.repo.GetAllClasses()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, classes)
}

// GetClassesByDate handles GET /api/timetable/:date
func (h *TimetableHandler) GetClassesByDate(c echo.Context) error {
	dateStr := c.Param("date")

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid date format. Use YYYY-MM-DD"})
	}

	classes, err := h.repo.GetClassesByDate(date)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, classes)
}

// AddClass handles POST /api/timetable
func (h *TimetableHandler) AddClass(c echo.Context) error {
	var class models.Class

	if err := json.NewDecoder(c.Request().Body).Decode(&class); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate required fields
	if class.Name == "" || class.Code == "" || class.Day == "" ||
		class.StartTime == "" || class.EndTime == "" ||
		class.Location == "" || class.Professor == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Missing required fields"})
	}

	newClass, err := h.repo.AddClass(class)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusCreated, newClass)
}

// UpdateClass handles PUT /api/timetable/:id
func (h *TimetableHandler) UpdateClass(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	var class models.Class
	if err := json.NewDecoder(c.Request().Body).Decode(&class); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate required fields
	if class.Name == "" || class.Code == "" || class.Day == "" ||
		class.StartTime == "" || class.EndTime == "" ||
		class.Location == "" || class.Professor == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Missing required fields"})
	}

	updatedClass, err := h.repo.UpdateClass(id, class)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, updatedClass)
}

// DeleteClass handles DELETE /api/timetable/:id
func (h *TimetableHandler) DeleteClass(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	if err := h.repo.DeleteClass(id); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.NoContent(http.StatusNoContent)
}
