package handlers

import (
	"encoding/json"
	"konnect_backend/internal/models"
	"konnect_backend/internal/repository"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
)

// HousingHandler handles housing-related requests
type HousingHandler struct {
	repo *repository.HousingRepository
}

// NewHousingHandler creates a new housing handler
func NewHousingHandler() *HousingHandler {
	return &HousingHandler{
		repo: repository.NewHousingRepository(),
	}
}

// GetAllHousing handles GET /api/housing
func (h *HousingHandler) GetAllHousing(c echo.Context) error {
	housing, err := h.repo.GetAllHousing()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, housing)
}

// GetHousingByID handles GET /api/housing/:id
func (h *HousingHandler) GetHousingByID(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	housing, err := h.repo.GetHousingByID(id)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, housing)
}

// SearchHousing handles GET /api/housing/search
func (h *HousingHandler) SearchHousing(c echo.Context) error {
	// Parse query parameters
	minRentStr := c.QueryParam("minRent")
	maxRentStr := c.QueryParam("maxRent")
	minBedsStr := c.QueryParam("minBeds")
	maxBedsStr := c.QueryParam("maxBeds")
	furnishedStr := c.QueryParam("furnished")

	var minRent, maxRent float64
	var minBeds, maxBeds int
	var furnished *bool

	// Parse min rent
	if minRentStr != "" {
		val, err := strconv.ParseFloat(minRentStr, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid minRent parameter"})
		}
		minRent = val
	}

	// Parse max rent
	if maxRentStr != "" {
		val, err := strconv.ParseFloat(maxRentStr, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid maxRent parameter"})
		}
		maxRent = val
	}

	// Parse min beds
	if minBedsStr != "" {
		val, err := strconv.Atoi(minBedsStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid minBeds parameter"})
		}
		minBeds = val
	}

	// Parse max beds
	if maxBedsStr != "" {
		val, err := strconv.Atoi(maxBedsStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid maxBeds parameter"})
		}
		maxBeds = val
	}

	// Parse furnished
	if furnishedStr != "" {
		val, err := strconv.ParseBool(furnishedStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid furnished parameter"})
		}
		furnished = &val
	}

	// Call repository with filters
	housing, err := h.repo.GetHousingByFilters(minRent, maxRent, minBeds, maxBeds, furnished)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, housing)
}

// AddHousing handles POST /api/housing
func (h *HousingHandler) AddHousing(c echo.Context) error {
	var housing models.Housing

	if err := json.NewDecoder(c.Request().Body).Decode(&housing); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate required fields
	if housing.Name == "" || housing.Address == "" || housing.Rent <= 0 ||
		housing.Bedrooms <= 0 || housing.Bathrooms <= 0 || housing.SquareFeet <= 0 ||
		housing.Description == "" || housing.Availability == "" ||
		housing.ContactName == "" || housing.ContactEmail == "" || housing.ContactPhone == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Missing required fields"})
	}

	// Ensure arrays are initialized
	if housing.Amenities == nil {
		housing.Amenities = []string{}
	}
	if housing.Images == nil {
		housing.Images = []string{}
	}

	newHousing, err := h.repo.AddHousing(housing)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusCreated, newHousing)
}

// UpdateHousing handles PUT /api/housing/:id
func (h *HousingHandler) UpdateHousing(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	var housing models.Housing
	if err := json.NewDecoder(c.Request().Body).Decode(&housing); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate required fields
	if housing.Name == "" || housing.Address == "" || housing.Rent <= 0 ||
		housing.Bedrooms <= 0 || housing.Bathrooms <= 0 || housing.SquareFeet <= 0 ||
		housing.Description == "" || housing.Availability == "" ||
		housing.ContactName == "" || housing.ContactEmail == "" || housing.ContactPhone == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Missing required fields"})
	}

	// Ensure arrays are initialized
	if housing.Amenities == nil {
		housing.Amenities = []string{}
	}
	if housing.Images == nil {
		housing.Images = []string{}
	}

	updatedHousing, err := h.repo.UpdateHousing(id, housing)
	if err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, updatedHousing)
}

// DeleteHousing handles DELETE /api/housing/:id
func (h *HousingHandler) DeleteHousing(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	if err := h.repo.DeleteHousing(id); err != nil {
		return c.JSON(http.StatusNotFound, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusNoContent, nil)
}

// GetPaginatedHousing handles GET /api/housing/paginated
func (h *HousingHandler) GetPaginatedHousing(c echo.Context) error {
	// Parse pagination parameters
	pageStr := c.QueryParam("page")
	pageSizeStr := c.QueryParam("pageSize")

	page := 1
	pageSize := 10

	if pageStr != "" {
		val, err := strconv.Atoi(pageStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid page parameter"})
		}
		page = val
	}

	if pageSizeStr != "" {
		val, err := strconv.Atoi(pageSizeStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid pageSize parameter"})
		}
		pageSize = val
	}

	response, err := h.repo.GetPaginatedHousing(page, pageSize)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, response)
}

// SearchHousingPaginated handles GET /api/housing/search/paginated
func (h *HousingHandler) SearchHousingPaginated(c echo.Context) error {
	// Parse pagination parameters
	pageStr := c.QueryParam("page")
	pageSizeStr := c.QueryParam("pageSize")

	page := 1
	pageSize := 10

	if pageStr != "" {
		val, err := strconv.Atoi(pageStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid page parameter"})
		}
		page = val
	}

	if pageSizeStr != "" {
		val, err := strconv.Atoi(pageSizeStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid pageSize parameter"})
		}
		pageSize = val
	}

	// Parse filter parameters
	minRentStr := c.QueryParam("minRent")
	maxRentStr := c.QueryParam("maxRent")
	minBedsStr := c.QueryParam("minBeds")
	maxBedsStr := c.QueryParam("maxBeds")
	minBathsStr := c.QueryParam("minBaths")
	maxBathsStr := c.QueryParam("maxBaths")
	furnishedStr := c.QueryParam("furnished")

	var minRent, maxRent, minBaths, maxBaths float64
	var minBeds, maxBeds int
	var furnished *bool

	// Parse min rent
	if minRentStr != "" {
		val, err := strconv.ParseFloat(minRentStr, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid minRent parameter"})
		}
		minRent = val
	}

	// Parse max rent
	if maxRentStr != "" {
		val, err := strconv.ParseFloat(maxRentStr, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid maxRent parameter"})
		}
		maxRent = val
	}

	// Parse min beds
	if minBedsStr != "" {
		val, err := strconv.Atoi(minBedsStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid minBeds parameter"})
		}
		minBeds = val
	}

	// Parse max beds
	if maxBedsStr != "" {
		val, err := strconv.Atoi(maxBedsStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid maxBeds parameter"})
		}
		maxBeds = val
	}

	// Parse min baths
	if minBathsStr != "" {
		val, err := strconv.ParseFloat(minBathsStr, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid minBaths parameter"})
		}
		minBaths = val
	}

	// Parse max baths
	if maxBathsStr != "" {
		val, err := strconv.ParseFloat(maxBathsStr, 64)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid maxBaths parameter"})
		}
		maxBaths = val
	}

	// Parse furnished
	if furnishedStr != "" {
		val, err := strconv.ParseBool(furnishedStr)
		if err != nil {
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid furnished parameter"})
		}
		furnished = &val
	}

	// Call repository with filters and pagination
	response, err := h.repo.GetPaginatedHousingByFilters(
		page, pageSize, minRent, maxRent, minBeds, maxBeds, minBaths, maxBaths, furnished,
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, response)
}

// RegisterRoutes registers all housing routes
func (h *HousingHandler) RegisterRoutes(e *echo.Echo) {
	e.GET("/api/housing", h.GetAllHousing)
	e.GET("/api/housing/paginated", h.GetPaginatedHousing)
	e.GET("/api/housing/search", h.SearchHousing)
	e.GET("/api/housing/search/paginated", h.SearchHousingPaginated)
	e.GET("/api/housing/:id", h.GetHousingByID)
	e.POST("/api/housing", h.AddHousing)
	e.PUT("/api/housing/:id", h.UpdateHousing)
	e.DELETE("/api/housing/:id", h.DeleteHousing)
}

// InitTable initializes the housing table
func (h *HousingHandler) InitTable() error {
	return h.repo.InitTable()
}
