package handlers

import (
	"konnect_backend/internal/cache"
	"net/http"

	"github.com/labstack/echo/v4"
)

// HealthHandler handles health check endpoints
type HealthHandler struct{}

// NewHealthHandler creates a new health handler
func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

// CheckRedis tests Redis connectivity
func (h *HealthHandler) CheckRedis(c echo.Context) error {
	if cache.Client == nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]string{
			"status": "Redis client not initialized",
		})
	}

	// Try to ping Redis
	result, err := cache.Client.Ping(cache.Ctx).Result()
	if err != nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]string{
			"status": "Redis connection failed",
			"error":  err.Error(),
		})
	}

	return c.JSON(http.StatusOK, map[string]string{
		"status": "Redis connection successful",
		"ping":   result,
	})
}

// ClearRedisCache clears all timetable cache data
func (h *HealthHandler) ClearRedisCache(c echo.Context) error {
	if cache.Client == nil {
		return c.JSON(http.StatusServiceUnavailable, map[string]string{
			"status": "Redis client not initialized",
		})
	}

	// Clear all timetable cache
	cache.InvalidateCache()

	return c.JSON(http.StatusOK, map[string]string{
		"status": "Redis cache cleared successfully",
	})
}
