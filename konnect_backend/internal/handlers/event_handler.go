package handlers

import (
	"encoding/json"
	"konnect_backend/internal/models"
	"konnect_backend/internal/repository"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
)

// EventHandler handles event-related requests
type EventHandler struct {
	repo *repository.EventRepository
}

// NewEventHandler creates a new event handler
func NewEventHandler() *EventHandler {
	return &EventHandler{
		repo: repository.NewEventRepository(),
	}
}

// GetAllEvents handles GET /api/events
func (h *EventHandler) GetAllEvents(c echo.Context) error {
	events, err := h.repo.GetAllEvents()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, events)
}

// GetEventsByDate handles GET /api/events/date/:date
func (h *EventHandler) GetEventsByDate(c echo.Context) error {
	dateStr := c.Param("date")

	// No need to parse the date anymore, just pass the string
	events, err := h.repo.GetEventsByDate(dateStr)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, events)
}

// GetEventsByCategory handles GET /api/events/category/:category
func (h *EventHandler) GetEventsByCategory(c echo.Context) error {
	category := c.Param("category")

	events, err := h.repo.GetEventsByCategory(category)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, events)
}

// AddEvent handles POST /api/events
func (h *EventHandler) AddEvent(c echo.Context) error {
	var event models.Event

	if err := json.NewDecoder(c.Request().Body).Decode(&event); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate required fields
	if event.Name == "" || event.Description == "" || event.Location == "" ||
		event.StartTime == "" || event.EndTime == "" || event.Category == "" ||
		event.Organizer == "" || event.Date == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Missing required fields"})
	}

	newEvent, err := h.repo.AddEvent(event)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusCreated, newEvent)
}

// UpdateEvent handles PUT /api/events/:id
func (h *EventHandler) UpdateEvent(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	var event models.Event
	if err := json.NewDecoder(c.Request().Body).Decode(&event); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate required fields
	if event.Name == "" || event.Description == "" || event.Location == "" ||
		event.StartTime == "" || event.EndTime == "" || event.Category == "" ||
		event.Organizer == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Missing required fields"})
	}

	// If date is not provided, use current date in YYYY-MM-DD format
	if event.Date == "" {
		event.Date = time.Now().Format("2006-01-02")
	}

	updatedEvent, err := h.repo.UpdateEvent(id, event)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.JSON(http.StatusOK, updatedEvent)
}

// DeleteEvent handles DELETE /api/events/:id
func (h *EventHandler) DeleteEvent(c echo.Context) error {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid ID"})
	}

	if err := h.repo.DeleteEvent(id); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
	}

	return c.NoContent(http.StatusNoContent)
}
