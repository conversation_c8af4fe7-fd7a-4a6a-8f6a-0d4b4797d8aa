package repository

import (
	"context"
	"fmt"
	"konnect_backend/internal/db"
	"konnect_backend/internal/models"
	"log"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// EventRepository handles database operations for events
type EventRepository struct {
	Pool *pgxpool.Pool
}

// NewEventRepository creates a new event repository and initializes the table
func NewEventRepository() *EventRepository {
	repo := &EventRepository{
		Pool: db.Pool,
	}

	// Initialize the table
	if err := repo.InitTable(); err != nil {
		fmt.Printf("Error initializing events table: %v\n", err)
	}

	return repo
}

// InitTable creates the events table if it doesn't exist
func (r *EventRepository) InitTable() error {
	ctx := context.Background()

	createTableSQL := `
    CREATE TABLE IF NOT EXISTS events (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT NOT NULL,
        location VARCHAR(100) NOT NULL,
        date VARCHAR(10) NOT NULL,
        start_time VARCHAR(10) NOT NULL,
        end_time VARCHAR(10) NOT NULL,
        category VARCHAR(50) NOT NULL,
        organizer VARCHAR(100) NOT NULL,
        is_reminded BOOLEAN DEFAULT false,
        is_favorite BOOLEAN DEFAULT false,
        attendees INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`

	_, err := r.Pool.Exec(ctx, createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create events table: %w", err)
	}

	return nil
}

// GetAllEvents retrieves all events from the database
func (r *EventRepository) GetAllEvents() ([]models.Event, error) {
	log.Println("🔴 DB ACCESS: Retrieving all events from database (no cache implemented yet)")
	ctx := context.Background()

	query := `SELECT id, name, description, location, date, start_time, end_time, 
              category, organizer, is_reminded, is_favorite, attendees 
              FROM events ORDER BY date, start_time`

	rows, err := r.Pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var events []models.Event
	for rows.Next() {
		var event models.Event
		if err := rows.Scan(
			&event.ID, &event.Name, &event.Description, &event.Location,
			&event.Date, &event.StartTime, &event.EndTime, &event.Category,
			&event.Organizer, &event.IsReminded, &event.IsFavorite, &event.Attendees,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}
		events = append(events, event)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	return events, nil
}

// GetEventsByDate retrieves events for a specific date
func (r *EventRepository) GetEventsByDate(dateStr string) ([]models.Event, error) {
	log.Printf("🔴 DB ACCESS: Retrieving events for date %s from database (no cache implemented yet)", dateStr)
	ctx := context.Background()

	query := `SELECT id, name, description, location, date, start_time, end_time, 
              category, organizer, is_reminded, is_favorite, attendees 
              FROM events WHERE date = $1 ORDER BY start_time`

	rows, err := r.Pool.Query(ctx, query, dateStr)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var events []models.Event
	for rows.Next() {
		var event models.Event
		if err := rows.Scan(
			&event.ID, &event.Name, &event.Description, &event.Location,
			&event.Date, &event.StartTime, &event.EndTime, &event.Category,
			&event.Organizer, &event.IsReminded, &event.IsFavorite, &event.Attendees,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}
		events = append(events, event)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	return events, nil
}

// GetEventsByCategory retrieves events for a specific category
func (r *EventRepository) GetEventsByCategory(category string) ([]models.Event, error) {
	ctx := context.Background()

	query := `SELECT id, name, description, location, date, start_time, end_time, 
              category, organizer, is_reminded, is_favorite, attendees 
              FROM events WHERE category = $1 ORDER BY date, start_time`

	rows, err := r.Pool.Query(ctx, query, category)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var events []models.Event
	for rows.Next() {
		var event models.Event
		if err := rows.Scan(
			&event.ID, &event.Name, &event.Description, &event.Location,
			&event.Date, &event.StartTime, &event.EndTime, &event.Category,
			&event.Organizer, &event.IsReminded, &event.IsFavorite, &event.Attendees,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}
		events = append(events, event)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	return events, nil
}

// AddEvent adds a new event to the database
func (r *EventRepository) AddEvent(event models.Event) (models.Event, error) {
	ctx := context.Background()

	query := `INSERT INTO events (name, description, location, date, start_time, end_time, 
              category, organizer, is_reminded, is_favorite, attendees) 
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) 
              RETURNING id, name, description, location, date, start_time, end_time, 
              category, organizer, is_reminded, is_favorite, attendees`

	var newEvent models.Event
	err := r.Pool.QueryRow(
		ctx,
		query,
		event.Name, event.Description, event.Location, event.Date,
		event.StartTime, event.EndTime, event.Category, event.Organizer,
		event.IsReminded, event.IsFavorite, event.Attendees,
	).Scan(
		&newEvent.ID, &newEvent.Name, &newEvent.Description, &newEvent.Location,
		&newEvent.Date, &newEvent.StartTime, &newEvent.EndTime, &newEvent.Category,
		&newEvent.Organizer, &newEvent.IsReminded, &newEvent.IsFavorite, &newEvent.Attendees,
	)

	if err != nil {
		return models.Event{}, fmt.Errorf("insert error: %w", err)
	}

	return newEvent, nil
}

// UpdateEvent updates an existing event
func (r *EventRepository) UpdateEvent(id int, event models.Event) (models.Event, error) {
	ctx := context.Background()

	query := `UPDATE events 
              SET name = $1, description = $2, location = $3, date = $4, 
                  start_time = $5, end_time = $6, category = $7, organizer = $8, 
                  is_reminded = $9, is_favorite = $10, attendees = $11 
              WHERE id = $12 
              RETURNING id, name, description, location, date, start_time, end_time, 
              category, organizer, is_reminded, is_favorite, attendees`

	var updatedEvent models.Event
	err := r.Pool.QueryRow(
		ctx,
		query,
		event.Name, event.Description, event.Location, event.Date,
		event.StartTime, event.EndTime, event.Category, event.Organizer,
		event.IsReminded, event.IsFavorite, event.Attendees, id,
	).Scan(
		&updatedEvent.ID, &updatedEvent.Name, &updatedEvent.Description, &updatedEvent.Location,
		&updatedEvent.Date, &updatedEvent.StartTime, &updatedEvent.EndTime, &updatedEvent.Category,
		&updatedEvent.Organizer, &updatedEvent.IsReminded, &updatedEvent.IsFavorite, &updatedEvent.Attendees,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return models.Event{}, fmt.Errorf("no event found with id %d", id)
		}
		return models.Event{}, fmt.Errorf("update error: %w", err)
	}

	return updatedEvent, nil
}

// DeleteEvent deletes an event by ID
func (r *EventRepository) DeleteEvent(id int) error {
	ctx := context.Background()

	query := `DELETE FROM events WHERE id = $1`

	commandTag, err := r.Pool.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("delete error: %w", err)
	}

	if commandTag.RowsAffected() == 0 {
		return fmt.Errorf("no event found with id %d", id)
	}

	return nil
}
