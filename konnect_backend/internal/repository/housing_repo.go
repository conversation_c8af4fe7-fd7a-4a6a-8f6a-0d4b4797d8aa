package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"konnect_backend/internal/db"
	"konnect_backend/internal/models"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// HousingRepository handles database operations for housing listings
type HousingRepository struct {
	Pool *pgxpool.Pool
}

// NewHousingRepository creates a new housing repository
func NewHousingRepository() *HousingRepository {
	return &HousingRepository{
		Pool: db.Pool,
	}
}

// InitTable creates the housing table if it doesn't exist
func (r *HousingRepository) InitTable() error {
	ctx := context.Background()

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS housing (
		id SERIAL PRIMARY KEY,
		name VARCHAR(100) NOT NULL,
		address TEXT NOT NULL,
		rent NUMERIC(10, 2) NOT NULL,
		bedrooms INTEGER NOT NULL,
		bathrooms NUMERIC(3, 1) NOT NULL,
		square_feet INTEGER NOT NULL,
		amenities JSONB NOT NULL,
		description TEXT NOT NULL,
		availability VARCHAR(50) NOT NULL,
		contact_name VARCHAR(100) NOT NULL,
		contact_email VARCHAR(100) NOT NULL,
		contact_phone VARCHAR(20) NOT NULL,
		images JSONB NOT NULL,
		is_furnished BOOLEAN DEFAULT false,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := r.Pool.Exec(ctx, createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create housing table: %w", err)
	}

	return nil
}

// GetAllHousing retrieves all housing listings from the database
func (r *HousingRepository) GetAllHousing() ([]models.Housing, error) {
	ctx := context.Background()

	query := `SELECT id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at 
              FROM housing ORDER BY created_at DESC`

	rows, err := r.Pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var listings []models.Housing
	for rows.Next() {
		var listing models.Housing
		var amenitiesJSON, imagesJSON []byte

		if err := rows.Scan(
			&listing.ID, &listing.Name, &listing.Address, &listing.Rent,
			&listing.Bedrooms, &listing.Bathrooms, &listing.SquareFeet,
			&amenitiesJSON, &listing.Description, &listing.Availability,
			&listing.ContactName, &listing.ContactEmail, &listing.ContactPhone,
			&imagesJSON, &listing.IsFurnished, &listing.CreatedAt,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}

		// Parse JSON arrays
		if err := json.Unmarshal(amenitiesJSON, &listing.Amenities); err != nil {
			return nil, fmt.Errorf("amenities unmarshal error: %w", err)
		}
		if err := json.Unmarshal(imagesJSON, &listing.Images); err != nil {
			return nil, fmt.Errorf("images unmarshal error: %w", err)
		}

		listings = append(listings, listing)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	return listings, nil
}

// GetHousingByID retrieves a specific housing listing by ID
func (r *HousingRepository) GetHousingByID(id int) (models.Housing, error) {
	ctx := context.Background()

	query := `SELECT id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at 
              FROM housing WHERE id = $1`

	var listing models.Housing
	var amenitiesJSON, imagesJSON []byte

	err := r.Pool.QueryRow(ctx, query, id).Scan(
		&listing.ID, &listing.Name, &listing.Address, &listing.Rent,
		&listing.Bedrooms, &listing.Bathrooms, &listing.SquareFeet,
		&amenitiesJSON, &listing.Description, &listing.Availability,
		&listing.ContactName, &listing.ContactEmail, &listing.ContactPhone,
		&imagesJSON, &listing.IsFurnished, &listing.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return models.Housing{}, fmt.Errorf("no housing found with id %d", id)
		}
		return models.Housing{}, fmt.Errorf("query error: %w", err)
	}

	// Parse JSON arrays
	if err := json.Unmarshal(amenitiesJSON, &listing.Amenities); err != nil {
		return models.Housing{}, fmt.Errorf("amenities unmarshal error: %w", err)
	}
	if err := json.Unmarshal(imagesJSON, &listing.Images); err != nil {
		return models.Housing{}, fmt.Errorf("images unmarshal error: %w", err)
	}

	return listing, nil
}

// GetHousingByFilters searches for housing with specified filters
func (r *HousingRepository) GetHousingByFilters(minRent, maxRent float64, minBeds, maxBeds int, furnished *bool) ([]models.Housing, error) {
	ctx := context.Background()

	// Build dynamic query based on provided filters
	query := `SELECT id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at 
              FROM housing WHERE 1=1`

	var args []interface{}
	var argPos int = 1

	if minRent > 0 {
		query += fmt.Sprintf(" AND rent >= $%d", argPos)
		args = append(args, minRent)
		argPos++
	}

	if maxRent > 0 {
		query += fmt.Sprintf(" AND rent <= $%d", argPos)
		args = append(args, maxRent)
		argPos++
	}

	if minBeds > 0 {
		query += fmt.Sprintf(" AND bedrooms >= $%d", argPos)
		args = append(args, minBeds)
		argPos++
	}

	if maxBeds > 0 {
		query += fmt.Sprintf(" AND bedrooms <= $%d", argPos)
		args = append(args, maxBeds)
		argPos++
	}

	if furnished != nil {
		query += fmt.Sprintf(" AND is_furnished = $%d", argPos)
		args = append(args, *furnished)
		argPos++
	}

	query += " ORDER BY created_at DESC"

	rows, err := r.Pool.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var listings []models.Housing
	for rows.Next() {
		var listing models.Housing
		var amenitiesJSON, imagesJSON []byte

		if err := rows.Scan(
			&listing.ID, &listing.Name, &listing.Address, &listing.Rent,
			&listing.Bedrooms, &listing.Bathrooms, &listing.SquareFeet,
			&amenitiesJSON, &listing.Description, &listing.Availability,
			&listing.ContactName, &listing.ContactEmail, &listing.ContactPhone,
			&imagesJSON, &listing.IsFurnished, &listing.CreatedAt,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}

		// Parse JSON arrays
		if err := json.Unmarshal(amenitiesJSON, &listing.Amenities); err != nil {
			return nil, fmt.Errorf("amenities unmarshal error: %w", err)
		}
		if err := json.Unmarshal(imagesJSON, &listing.Images); err != nil {
			return nil, fmt.Errorf("images unmarshal error: %w", err)
		}

		listings = append(listings, listing)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	return listings, nil
}

// AddHousing adds a new housing listing to the database
func (r *HousingRepository) AddHousing(housing models.Housing) (models.Housing, error) {
	ctx := context.Background()

	// Convert slices to JSON
	amenitiesJSON, err := json.Marshal(housing.Amenities)
	if err != nil {
		return models.Housing{}, fmt.Errorf("amenities marshal error: %w", err)
	}

	imagesJSON, err := json.Marshal(housing.Images)
	if err != nil {
		return models.Housing{}, fmt.Errorf("images marshal error: %w", err)
	}

	query := `INSERT INTO housing (name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished) 
              VALUES ($1, $2, $3, $4, $5, $6, $7::jsonb, $8, $9, $10, $11, $12, $13::jsonb, $14) 
              RETURNING id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at`

	var newHousing models.Housing
	var returnedAmenitiesJSON, returnedImagesJSON []byte

	err = r.Pool.QueryRow(
		ctx,
		query,
		housing.Name, housing.Address, housing.Rent, housing.Bedrooms,
		housing.Bathrooms, housing.SquareFeet, string(amenitiesJSON), housing.Description,
		housing.Availability, housing.ContactName, housing.ContactEmail,
		housing.ContactPhone, string(imagesJSON), housing.IsFurnished,
	).Scan(
		&newHousing.ID, &newHousing.Name, &newHousing.Address, &newHousing.Rent,
		&newHousing.Bedrooms, &newHousing.Bathrooms, &newHousing.SquareFeet,
		&returnedAmenitiesJSON, &newHousing.Description, &newHousing.Availability,
		&newHousing.ContactName, &newHousing.ContactEmail, &newHousing.ContactPhone,
		&returnedImagesJSON, &newHousing.IsFurnished, &newHousing.CreatedAt,
	)

	if err != nil {
		return models.Housing{}, fmt.Errorf("insert error: %w", err)
	}

	// Parse returned JSON arrays
	if err := json.Unmarshal(returnedAmenitiesJSON, &newHousing.Amenities); err != nil {
		return models.Housing{}, fmt.Errorf("amenities unmarshal error: %w", err)
	}
	if err := json.Unmarshal(returnedImagesJSON, &newHousing.Images); err != nil {
		return models.Housing{}, fmt.Errorf("images unmarshal error: %w", err)
	}

	return newHousing, nil
}

// UpdateHousing updates an existing housing listing
func (r *HousingRepository) UpdateHousing(id int, housing models.Housing) (models.Housing, error) {
	ctx := context.Background()

	// Convert slices to JSON
	amenitiesJSON, err := json.Marshal(housing.Amenities)
	if err != nil {
		return models.Housing{}, fmt.Errorf("amenities marshal error: %w", err)
	}

	imagesJSON, err := json.Marshal(housing.Images)
	if err != nil {
		return models.Housing{}, fmt.Errorf("images marshal error: %w", err)
	}

	query := `UPDATE housing 
              SET name = $1, address = $2, rent = $3, bedrooms = $4, 
                  bathrooms = $5, square_feet = $6, amenities = $7::jsonb, 
                  description = $8, availability = $9, contact_name = $10, 
                  contact_email = $11, contact_phone = $12, images = $13::jsonb, 
                  is_furnished = $14 
              WHERE id = $15 
              RETURNING id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at`

	var updatedHousing models.Housing
	var returnedAmenitiesJSON, returnedImagesJSON []byte

	err = r.Pool.QueryRow(
		ctx,
		query,
		housing.Name, housing.Address, housing.Rent, housing.Bedrooms,
		housing.Bathrooms, housing.SquareFeet, string(amenitiesJSON), housing.Description,
		housing.Availability, housing.ContactName, housing.ContactEmail,
		housing.ContactPhone, string(imagesJSON), housing.IsFurnished, id,
	).Scan(
		&updatedHousing.ID, &updatedHousing.Name, &updatedHousing.Address, &updatedHousing.Rent,
		&updatedHousing.Bedrooms, &updatedHousing.Bathrooms, &updatedHousing.SquareFeet,
		&returnedAmenitiesJSON, &updatedHousing.Description, &updatedHousing.Availability,
		&updatedHousing.ContactName, &updatedHousing.ContactEmail, &updatedHousing.ContactPhone,
		&returnedImagesJSON, &updatedHousing.IsFurnished, &updatedHousing.CreatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return models.Housing{}, fmt.Errorf("no housing found with id %d", id)
		}
		return models.Housing{}, fmt.Errorf("update error: %w", err)
	}

	// Parse returned JSON arrays
	if err := json.Unmarshal(returnedAmenitiesJSON, &updatedHousing.Amenities); err != nil {
		return models.Housing{}, fmt.Errorf("amenities unmarshal error: %w", err)
	}
	if err := json.Unmarshal(returnedImagesJSON, &updatedHousing.Images); err != nil {
		return models.Housing{}, fmt.Errorf("images unmarshal error: %w", err)
	}

	return updatedHousing, nil
}

// DeleteHousing removes a housing listing from the database
func (r *HousingRepository) DeleteHousing(id int) error {
	ctx := context.Background()

	query := `DELETE FROM housing WHERE id = $1`

	commandTag, err := r.Pool.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("delete error: %w", err)
	}

	if commandTag.RowsAffected() == 0 {
		return fmt.Errorf("no housing found with id %d", id)
	}

	return nil
}

// GetPaginatedHousing retrieves housing listings with pagination
func (r *HousingRepository) GetPaginatedHousing(page, pageSize int) (models.PaginatedHousingResponse, error) {
	ctx := context.Background()

	// Default values if invalid parameters are provided
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	// Get total count
	var totalCount int
	countQuery := `SELECT COUNT(*) FROM housing`
	err := r.Pool.QueryRow(ctx, countQuery).Scan(&totalCount)
	if err != nil {
		return models.PaginatedHousingResponse{}, fmt.Errorf("count query error: %w", err)
	}

	// Calculate page count
	pageCount := (totalCount + pageSize - 1) / pageSize

	// Get listings for current page
	query := `SELECT id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at 
              FROM housing ORDER BY created_at DESC LIMIT $1 OFFSET $2`

	rows, err := r.Pool.Query(ctx, query, pageSize, offset)
	if err != nil {
		return models.PaginatedHousingResponse{}, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var listings []models.Housing
	for rows.Next() {
		var listing models.Housing
		var amenitiesJSON, imagesJSON []byte

		if err := rows.Scan(
			&listing.ID, &listing.Name, &listing.Address, &listing.Rent,
			&listing.Bedrooms, &listing.Bathrooms, &listing.SquareFeet,
			&amenitiesJSON, &listing.Description, &listing.Availability,
			&listing.ContactName, &listing.ContactEmail, &listing.ContactPhone,
			&imagesJSON, &listing.IsFurnished, &listing.CreatedAt,
		); err != nil {
			return models.PaginatedHousingResponse{}, fmt.Errorf("scan error: %w", err)
		}

		// Parse JSON arrays
		if err := json.Unmarshal(amenitiesJSON, &listing.Amenities); err != nil {
			return models.PaginatedHousingResponse{}, fmt.Errorf("amenities unmarshal error: %w", err)
		}
		if err := json.Unmarshal(imagesJSON, &listing.Images); err != nil {
			return models.PaginatedHousingResponse{}, fmt.Errorf("images unmarshal error: %w", err)
		}

		listings = append(listings, listing)
	}

	if err = rows.Err(); err != nil {
		return models.PaginatedHousingResponse{}, fmt.Errorf("rows error: %w", err)
	}

	return models.PaginatedHousingResponse{
		Listings:    listings,
		TotalCount:  totalCount,
		PageCount:   pageCount,
		CurrentPage: page,
		PageSize:    pageSize,
	}, nil
}

// GetPaginatedHousingByFilters searches for housing with specified filters and pagination
func (r *HousingRepository) GetPaginatedHousingByFilters(
	page, pageSize int,
	minRent, maxRent float64,
	minBeds, maxBeds int,
	minBaths, maxBaths float64,
	furnished *bool,
) (models.PaginatedHousingResponse, error) {
	ctx := context.Background()

	// Default values if invalid parameters are provided
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	// Build dynamic query based on provided filters
	baseQuery := `FROM housing WHERE 1=1`
	var args []interface{}
	var argPos int = 1

	if minRent > 0 {
		baseQuery += fmt.Sprintf(" AND rent >= $%d", argPos)
		args = append(args, minRent)
		argPos++
	}

	if maxRent > 0 {
		baseQuery += fmt.Sprintf(" AND rent <= $%d", argPos)
		args = append(args, maxRent)
		argPos++
	}

	if minBeds > 0 {
		baseQuery += fmt.Sprintf(" AND bedrooms >= $%d", argPos)
		args = append(args, minBeds)
		argPos++
	}

	if maxBeds > 0 {
		baseQuery += fmt.Sprintf(" AND bedrooms <= $%d", argPos)
		args = append(args, maxBeds)
		argPos++
	}

	if minBaths > 0 {
		baseQuery += fmt.Sprintf(" AND bathrooms >= $%d", argPos)
		args = append(args, minBaths)
		argPos++
	}

	if maxBaths > 0 {
		baseQuery += fmt.Sprintf(" AND bathrooms <= $%d", argPos)
		args = append(args, maxBaths)
		argPos++
	}

	if furnished != nil {
		baseQuery += fmt.Sprintf(" AND is_furnished = $%d", argPos)
		args = append(args, *furnished)
		argPos++
	}

	// Get total count with filters
	var totalCount int
	countQuery := `SELECT COUNT(*) ` + baseQuery
	err := r.Pool.QueryRow(ctx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		return models.PaginatedHousingResponse{}, fmt.Errorf("count query error: %w", err)
	}

	// Calculate page count
	pageCount := (totalCount + pageSize - 1) / pageSize

	// Get listings for current page with filters
	query := `SELECT id, name, address, rent, bedrooms, bathrooms, square_feet, 
              amenities, description, availability, contact_name, contact_email, 
              contact_phone, images, is_furnished, created_at 
              ` + baseQuery + ` ORDER BY created_at DESC LIMIT $` + fmt.Sprintf("%d", argPos) +
		` OFFSET $` + fmt.Sprintf("%d", argPos+1)

	args = append(args, pageSize, offset)

	rows, err := r.Pool.Query(ctx, query, args...)
	if err != nil {
		return models.PaginatedHousingResponse{}, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var listings []models.Housing
	for rows.Next() {
		var listing models.Housing
		var amenitiesJSON, imagesJSON []byte

		if err := rows.Scan(
			&listing.ID, &listing.Name, &listing.Address, &listing.Rent,
			&listing.Bedrooms, &listing.Bathrooms, &listing.SquareFeet,
			&amenitiesJSON, &listing.Description, &listing.Availability,
			&listing.ContactName, &listing.ContactEmail, &listing.ContactPhone,
			&imagesJSON, &listing.IsFurnished, &listing.CreatedAt,
		); err != nil {
			return models.PaginatedHousingResponse{}, fmt.Errorf("scan error: %w", err)
		}

		// Parse JSON arrays
		if err := json.Unmarshal(amenitiesJSON, &listing.Amenities); err != nil {
			return models.PaginatedHousingResponse{}, fmt.Errorf("amenities unmarshal error: %w", err)
		}
		if err := json.Unmarshal(imagesJSON, &listing.Images); err != nil {
			return models.PaginatedHousingResponse{}, fmt.Errorf("images unmarshal error: %w", err)
		}

		listings = append(listings, listing)
	}

	if err = rows.Err(); err != nil {
		return models.PaginatedHousingResponse{}, fmt.Errorf("rows error: %w", err)
	}

	return models.PaginatedHousingResponse{
		Listings:    listings,
		TotalCount:  totalCount,
		PageCount:   pageCount,
		CurrentPage: page,
		PageSize:    pageSize,
	}, nil
}
