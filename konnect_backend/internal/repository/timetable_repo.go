package repository

import (
	"context"
	"fmt"
	"konnect_backend/internal/cache"
	"konnect_backend/internal/db"
	"konnect_backend/internal/models"
	"log"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// TimetableRepository handles database operations for timetable
type TimetableRepository struct {
	Pool *pgxpool.Pool
}

// NewTimetableRepository creates a new timetable repository and initializes the table
func NewTimetableRepository() *TimetableRepository {
	repo := &TimetableRepository{
		Pool: db.Pool,
	}

	// Initialize the table
	if err := repo.InitTable(); err != nil {
		fmt.Printf("Error initializing timetable: %v\n", err)
	}

	return repo
}

// InitTable creates the timetable table if it doesn't exist
func (r *TimetableRepository) InitTable() error {
	ctx := context.Background()

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS timetable (
		id SERIAL PRIMARY KEY,
		name VARCHAR(100) NOT NULL,
		code VARCHAR(20) NOT NULL,
		day VARCHAR(10) NOT NULL,
		start_time VARCHAR(10) NOT NULL,
		end_time VARCHAR(10) NOT NULL,
		location VARCHAR(100) NOT NULL,
		professor VARCHAR(100) NOT NULL,
		reminders BOOLEAN DEFAULT false,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	)`

	_, err := r.Pool.Exec(ctx, createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create timetable: %w", err)
	}

	return nil
}

// GetAllClasses retrieves all classes from the database
func (r *TimetableRepository) GetAllClasses() ([]models.Class, error) {
	// Try to get from cache first
	if classes, found := cache.GetAllClasses(); found {
		log.Println("🔵 CACHE HIT: Retrieved all classes from Redis cache")
		return classes, nil
	}

	// Cache miss, get from database
	log.Println("🔴 CACHE MISS: Retrieving all classes from database")
	ctx := context.Background()

	query := `SELECT id, name, code, day, start_time, end_time, location, professor, reminders 
              FROM timetable ORDER BY day, start_time`

	rows, err := r.Pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var classes []models.Class
	for rows.Next() {
		var class models.Class
		if err := rows.Scan(
			&class.ID, &class.Name, &class.Code, &class.Day,
			&class.StartTime, &class.EndTime, &class.Location,
			&class.Professor, &class.Reminders,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}
		classes = append(classes, class)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	// Store in cache for future requests
	cache.SetAllClasses(classes)
	log.Println("🟢 CACHE UPDATE: Stored all classes in Redis cache")

	return classes, nil
}

// GetClassesByDate retrieves classes for a specific date
func (r *TimetableRepository) GetClassesByDate(date time.Time) ([]models.Class, error) {
	// Try to get from cache first
	if classes, found := cache.GetClassesByDate(date); found {
		dayNames := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
		dayName := dayNames[date.Weekday()]
		log.Printf("🔵 CACHE HIT: Retrieved classes for %s from Redis cache", dayName)
		return classes, nil
	}

	// Cache miss, get from database
	dayNames := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
	dayName := dayNames[date.Weekday()]
	log.Printf("🔴 CACHE MISS: Retrieving classes for %s from database", dayName)

	ctx := context.Background()

	query := `SELECT id, name, code, day, start_time, end_time, location, professor, reminders 
              FROM timetable WHERE day = $1 ORDER BY start_time`

	rows, err := r.Pool.Query(ctx, query, dayName)
	if err != nil {
		return nil, fmt.Errorf("query error: %w", err)
	}
	defer rows.Close()

	var classes []models.Class
	for rows.Next() {
		var class models.Class
		if err := rows.Scan(
			&class.ID, &class.Name, &class.Code, &class.Day,
			&class.StartTime, &class.EndTime, &class.Location,
			&class.Professor, &class.Reminders,
		); err != nil {
			return nil, fmt.Errorf("scan error: %w", err)
		}
		classes = append(classes, class)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %w", err)
	}

	// Store in cache for future requests
	cache.SetClassesByDate(date, classes)
	log.Printf("🟢 CACHE UPDATE: Stored classes for %s in Redis cache", dayName)

	return classes, nil
}

// AddClass adds a new class to the database
func (r *TimetableRepository) AddClass(class models.Class) (models.Class, error) {
	ctx := context.Background()

	query := `INSERT INTO timetable (name, code, day, start_time, end_time, location, professor, reminders) 
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
              RETURNING id, name, code, day, start_time, end_time, location, professor, reminders`

	var newClass models.Class
	err := r.Pool.QueryRow(
		ctx,
		query,
		class.Name, class.Code, class.Day, class.StartTime,
		class.EndTime, class.Location, class.Professor, class.Reminders,
	).Scan(
		&newClass.ID, &newClass.Name, &newClass.Code, &newClass.Day,
		&newClass.StartTime, &newClass.EndTime, &newClass.Location,
		&newClass.Professor, &newClass.Reminders,
	)

	if err != nil {
		return models.Class{}, fmt.Errorf("insert error: %w", err)
	}

	// Invalidate cache since we've added a new class
	cache.InvalidateCache()
	log.Println("🟠 CACHE INVALIDATED: Added new class, cleared timetable cache")

	// Cache the new class
	cache.SetClassByID(newClass)
	log.Printf("🟢 CACHE UPDATE: Stored new class ID %d in Redis cache", newClass.ID)

	return newClass, nil
}

// UpdateClass updates an existing class
func (r *TimetableRepository) UpdateClass(id int, class models.Class) (models.Class, error) {
	ctx := context.Background()

	query := `UPDATE timetable 
              SET name = $1, code = $2, day = $3, start_time = $4, 
                  end_time = $5, location = $6, professor = $7, reminders = $8 
              WHERE id = $9 
              RETURNING id, name, code, day, start_time, end_time, location, professor, reminders`

	var updatedClass models.Class
	err := r.Pool.QueryRow(
		ctx,
		query,
		class.Name, class.Code, class.Day, class.StartTime,
		class.EndTime, class.Location, class.Professor, class.Reminders, id,
	).Scan(
		&updatedClass.ID, &updatedClass.Name, &updatedClass.Code, &updatedClass.Day,
		&updatedClass.StartTime, &updatedClass.EndTime, &updatedClass.Location,
		&updatedClass.Professor, &updatedClass.Reminders,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return models.Class{}, fmt.Errorf("no class found with id %d", id)
		}
		return models.Class{}, fmt.Errorf("update error: %w", err)
	}

	// Invalidate cache for this class and collections
	cache.InvalidateClassByID(id)
	log.Printf("🟠 CACHE INVALIDATED: Updated class ID %d, cleared related cache", id)

	// Cache the updated class
	cache.SetClassByID(updatedClass)
	log.Printf("🟢 CACHE UPDATE: Stored updated class ID %d in Redis cache", id)

	return updatedClass, nil
}

// DeleteClass deletes a class by ID
func (r *TimetableRepository) DeleteClass(id int) error {
	ctx := context.Background()

	query := `DELETE FROM timetable WHERE id = $1`

	commandTag, err := r.Pool.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("delete error: %w", err)
	}

	if commandTag.RowsAffected() == 0 {
		return fmt.Errorf("no class found with id %d", id)
	}

	// Invalidate cache for this class and collections
	cache.InvalidateClassByID(id)
	log.Printf("🟠 CACHE INVALIDATED: Deleted class ID %d, cleared related cache", id)

	return nil
}
