package cache

import (
	"encoding/json"
	"fmt"
	"konnect_backend/internal/models"
	"log"
	"time"
)

const (
	// Key prefixes
	allClassesKey  = "timetable:all"
	classByDateKey = "timetable:date:%s"
	classByIDKey   = "timetable:id:%d"
)

// GetAllClasses retrieves all classes from cache
func GetAllClasses() ([]models.Class, bool) {
	if Client == nil {
		log.Println("Redis client not initialized, skipping cache lookup")
		return nil, false
	}

	data, err := Client.Get(Ctx, allClassesKey).Bytes()
	if err != nil {
		log.Printf("Cache miss: GetAllClasses - %v", err)
		return nil, false
	}

	var classes []models.Class
	if err := json.Unmarshal(data, &classes); err != nil {
		log.Printf("Error unmarshaling cached classes: %v", err)
		return nil, false
	}

	log.Printf("Cache hit: GetAllClasses - found %d classes", len(classes))
	return classes, true
}

// SetAllClasses stores all classes in cache
func SetAllClasses(classes []models.Class) {
	if Client == nil {
		return
	}

	data, err := json.Marshal(classes)
	if err != nil {
		log.Printf("Error marshaling classes for cache: %v", err)
		return
	}

	if err := Client.Set(Ctx, allClassesKey, data, DefaultTTL).Err(); err != nil {
		log.Printf("Error caching all classes: %v", err)
	} else {
		log.Printf("Successfully cached %d classes with TTL %v", len(classes), DefaultTTL)
	}
}

// GetClassesByDate retrieves classes for a specific date from cache
func GetClassesByDate(date time.Time) ([]models.Class, bool) {
	if Client == nil {
		return nil, false
	}

	// Convert date to day of week
	dayNames := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
	dayName := dayNames[date.Weekday()]
	key := fmt.Sprintf(classByDateKey, dayName)

	data, err := Client.Get(Ctx, key).Bytes()
	if err != nil {
		return nil, false
	}

	var classes []models.Class
	if err := json.Unmarshal(data, &classes); err != nil {
		log.Printf("Error unmarshaling cached classes by date: %v", err)
		return nil, false
	}

	log.Println("Cache hit: GetClassesByDate", dayName)
	return classes, true
}

// SetClassesByDate stores classes for a specific date in cache
func SetClassesByDate(date time.Time, classes []models.Class) {
	if Client == nil {
		return
	}

	// Convert date to day of week
	dayNames := []string{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
	dayName := dayNames[date.Weekday()]
	key := fmt.Sprintf(classByDateKey, dayName)

	data, err := json.Marshal(classes)
	if err != nil {
		log.Printf("Error marshaling classes by date for cache: %v", err)
		return
	}

	if err := Client.Set(Ctx, key, data, DefaultTTL).Err(); err != nil {
		log.Printf("Error caching classes by date: %v", err)
	}
}

// GetClassByID retrieves a class by ID from cache
func GetClassByID(id int) (models.Class, bool) {
	if Client == nil {
		return models.Class{}, false
	}

	key := fmt.Sprintf(classByIDKey, id)
	data, err := Client.Get(Ctx, key).Bytes()
	if err != nil {
		return models.Class{}, false
	}

	var class models.Class
	if err := json.Unmarshal(data, &class); err != nil {
		log.Printf("Error unmarshaling cached class: %v", err)
		return models.Class{}, false
	}

	log.Println("Cache hit: GetClassByID", id)
	return class, true
}

// SetClassByID stores a class by ID in cache
func SetClassByID(class models.Class) {
	if Client == nil {
		return
	}

	key := fmt.Sprintf(classByIDKey, class.ID)
	data, err := json.Marshal(class)
	if err != nil {
		log.Printf("Error marshaling class for cache: %v", err)
		return
	}

	if err := Client.Set(Ctx, key, data, DefaultTTL).Err(); err != nil {
		log.Printf("Error caching class by ID: %v", err)
	}
}

// InvalidateCache invalidates all timetable caches
func InvalidateCache() {
	if Client == nil {
		return
	}

	// Use scan to find all keys with the timetable prefix
	var keysDeleted int
	iter := Client.Scan(Ctx, 0, "timetable:*", 100).Iterator()
	for iter.Next(Ctx) {
		if err := Client.Del(Ctx, iter.Val()).Err(); err != nil {
			log.Printf("Error deleting cache key %s: %v", iter.Val(), err)
		} else {
			keysDeleted++
		}
	}
	if err := iter.Err(); err != nil {
		log.Printf("Error scanning cache keys: %v", err)
	}

	log.Printf("Timetable cache invalidated: %d keys deleted", keysDeleted)
}

// InvalidateClassByID invalidates cache for a specific class
func InvalidateClassByID(id int) {
	if Client == nil {
		return
	}

	key := fmt.Sprintf(classByIDKey, id)
	if err := Client.Del(Ctx, key).Err(); err != nil {
		log.Printf("Error deleting cache for class ID %d: %v", id, err)
	}

	// Also invalidate the collections that might contain this class
	InvalidateCache()
}
