package cache

import (
    "context"
    "fmt"
    "log"
    "os"
    "time"

    "github.com/redis/go-redis/v9"
)

var (
    // Client is the Redis client instance
    Client *redis.Client
    // Ctx is the context for Redis operations
    Ctx = context.Background()
)

// Initialize sets up the Redis connection
func Initialize() error {
    redisURL := os.Getenv("REDIS_URL")
    if redisURL == "" {
        redisURL = "localhost:6379"
    }

    redisPassword := os.Getenv("REDIS_PASSWORD")
    redisDB := 0

    Client = redis.NewClient(&redis.Options{
        Addr:     redisURL,
        Password: redisPassword,
        DB:       redisDB,
    })

    // Test the connection
    _, err := Client.Ping(Ctx).Result()
    if err != nil {
        return fmt.Errorf("failed to connect to Redis: %w", err)
    }

    log.Println("Connected to Redis successfully")
    return nil
}

// Close closes the Redis connection
func Close() {
    if Client != nil {
        if err := Client.Close(); err != nil {
            log.Printf("Error closing Redis connection: %v", err)
        }
    }
}

// DefaultTTL is the default time-to-live for cached items
const DefaultTTL = 30 * time.Minute