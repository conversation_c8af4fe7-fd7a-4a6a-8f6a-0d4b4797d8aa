#!/bin/bash
# Comprehensive test script for timetable API

# Base URL for the API
BASE_URL="ec2-3-145-32-152.us-east-2.compute.amazonaws.com:8080/api/timetable"

# Test 1: Get all classes
echo "Testing GET all classes..."
curl -X GET "$BASE_URL" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

# Test 2: Get classes for Monday, May 26, 2025
echo "Testing GET classes for Monday, May 26, 2025..."
curl -X GET "$BASE_URL/2025-05-26" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

# Test 3: Add 3 classes for Monday
echo "Adding 3 classes for Monday..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Introduction to Computer Science",
    "code": "CS101",
    "day": "Monday",
    "startTime": "09:00",
    "endTime": "10:30",
    "location": "Engineering Building, Room 101",
    "professor": "<PERSON><PERSON> <PERSON>",
    "reminders": true
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Calculus I",
    "code": "MATH101",
    "day": "Monday",
    "startTime": "11:00",
    "endTime": "12:30",
    "location": "Science Building, Room 203",
    "professor": "Dr. Johnson",
    "reminders": false
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Introduction to Psychology",
    "code": "PSYC101",
    "day": "Monday",
    "startTime": "14:00",
    "endTime": "15:30",
    "location": "Humanities Building, Room 305",
    "professor": "Dr. Williams",
    "reminders": true
  }'
echo -e "\n\n"

# Test 4: Add 3 classes for Tuesday
echo "Adding 3 classes for Tuesday..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Physics I",
    "code": "PHYS101",
    "day": "Tuesday",
    "startTime": "09:00",
    "endTime": "10:30",
    "location": "Science Building, Room 101",
    "professor": "Dr. Brown",
    "reminders": true
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "English Composition",
    "code": "ENG101",
    "day": "Tuesday",
    "startTime": "11:00",
    "endTime": "12:30",
    "location": "Humanities Building, Room 201",
    "professor": "Dr. Davis",
    "reminders": false
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Data Structures",
    "code": "CS201",
    "day": "Tuesday",
    "startTime": "14:00",
    "endTime": "15:30",
    "location": "Engineering Building, Room 202",
    "professor": "Dr. Wilson",
    "reminders": true
  }'
echo -e "\n\n"

# Test 5: Add 3 classes for Wednesday
echo "Adding 3 classes for Wednesday..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Introduction to Computer Science",
    "code": "CS101",
    "day": "Wednesday",
    "startTime": "09:00",
    "endTime": "10:30",
    "location": "Engineering Building, Room 101",
    "professor": "Dr. Smith",
    "reminders": true
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Calculus I",
    "code": "MATH101",
    "day": "Wednesday",
    "startTime": "11:00",
    "endTime": "12:30",
    "location": "Science Building, Room 203",
    "professor": "Dr. Johnson",
    "reminders": false
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Chemistry Lab",
    "code": "CHEM102",
    "day": "Wednesday",
    "startTime": "14:00",
    "endTime": "16:00",
    "location": "Science Building, Lab 105",
    "professor": "Dr. Miller",
    "reminders": true
  }'
echo -e "\n\n"

# Test 6: Add 3 classes for Thursday
echo "Adding 3 classes for Thursday..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Physics I",
    "code": "PHYS101",
    "day": "Thursday",
    "startTime": "09:00",
    "endTime": "10:30",
    "location": "Science Building, Room 101",
    "professor": "Dr. Brown",
    "reminders": true
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "English Composition",
    "code": "ENG101",
    "day": "Thursday",
    "startTime": "11:00",
    "endTime": "12:30",
    "location": "Humanities Building, Room 201",
    "professor": "Dr. Davis",
    "reminders": false
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Data Structures",
    "code": "CS201",
    "day": "Thursday",
    "startTime": "14:00",
    "endTime": "15:30",
    "location": "Engineering Building, Room 202",
    "professor": "Dr. Wilson",
    "reminders": true
  }'
echo -e "\n\n"

# Test 7: Add 3 classes for Friday
echo "Adding 3 classes for Friday..."
curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Database Systems",
    "code": "CS301",
    "day": "Friday",
    "startTime": "09:00",
    "endTime": "10:30",
    "location": "Engineering Building, Room 303",
    "professor": "Dr. Taylor",
    "reminders": true
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Statistics",
    "code": "MATH201",
    "day": "Friday",
    "startTime": "11:00",
    "endTime": "12:30",
    "location": "Science Building, Room 205",
    "professor": "Dr. Anderson",
    "reminders": false
  }'
echo -e "\n"

curl -X POST "$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Computer Networks",
    "code": "CS401",
    "day": "Friday",
    "startTime": "14:00",
    "endTime": "15:30",
    "location": "Engineering Building, Room 304",
    "professor": "Dr. Thomas",
    "reminders": true
  }'
echo -e "\n\n"

# Test 8: Get classes for each day of the week
echo "Testing GET classes for each day of the week..."
echo "Monday (2025-05-26):"
curl -X GET "$BASE_URL/2025-05-26" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

echo "Tuesday (2025-05-27):"
curl -X GET "$BASE_URL/2025-05-27" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

echo "Wednesday (2025-05-28):"
curl -X GET "$BASE_URL/2025-05-28" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

echo "Thursday (2025-05-29):"
curl -X GET "$BASE_URL/2025-05-29" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

echo "Friday (2025-05-30):"
curl -X GET "$BASE_URL/2025-05-30" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json"
echo -e "\n\n"

# Expected response format for each day:
# [
#   {
#     "id": 1,
#     "name": "Introduction to Computer Science",
#     "code": "CS101",
#     "day": "Monday",
#     "startTime": "09:00",
#     "endTime": "10:30",
#     "location": "Engineering Building, Room 101",
#     "professor": "Dr. Smith",
#     "reminders": true
#   },
#   {
#     "id": 2,
#     "name": "Calculus I",
#     "code": "MATH101",
#     "day": "Monday",
#     "startTime": "11:00",
#     "endTime": "12:30",
#     "location": "Science Building, Room 203",
#     "professor": "Dr. Johnson",
#     "reminders": false
#   },
#   {
#     "id": 3,
#     "name": "Introduction to Psychology",
#     "code": "PSYC101",
#     "day": "Monday",
#     "startTime": "14:00",
#     "endTime": "15:30",
#     "location": "Humanities Building, Room 305",
#     "professor": "Dr. Williams",
#     "reminders": true
#   }
# ]