import 'package:flutter/material.dart';
import '../routes/app_router.dart';

class MentorshipScreen extends StatefulWidget {
  const MentorshipScreen({super.key});

  @override
  State<MentorshipScreen> createState() => _MentorshipScreenState();
}

class _MentorshipScreenState extends State<MentorshipScreen> {
  int _currentStep = 0;
  final List<String> _selectedInterests = [];
  String? _selectedProgram;
  String? _selectedYear;
  String? _selectedGoal;

  final List<String> _interests = [
    'Academic Success',
    'Career Planning',
    'Research',
    'Internships',
    'Graduate School',
    'Work-Life Balance',
    'Campus Involvement',
    'Leadership',
    'Networking',
  ];

  final List<String> _programs = [
    'Computer Science',
    'Engineering',
    'Business',
    'Arts',
    'Medicine',
  ];

  final List<String> _years = [
    'First Year',
    'Second Year',
    'Third Year',
    'Fourth Year',
  ];

  final List<String> _goals = [
    'Improve academic performance',
    'Explore career options',
    'Get involved in research',
    'Find internship opportunities',
    'Prepare for graduate school',
    'Develop leadership skills',
  ];

  final List<Map<String, dynamic>> _mentors = [
    {
      'name': 'Dr. <PERSON>',
      'title': 'Associate Professor, Computer Science',
      'expertise': ['Programming', 'AI', 'Research Methods'],
      'bio':
          'Dr. Chen specializes in artificial intelligence and has mentored over 30 students in research projects.',
      'availability': 'Tuesdays and Thursdays, 2-4 PM',
      'rating': 4.8,
      'reviews': 15,
    },
    {
      'name': 'Prof. Michael Rodriguez',
      'title': 'Assistant Professor, Engineering',
      'expertise': ['Mechanical Design', 'Robotics', 'Project Management'],
      'bio':
          'Prof. Rodriguez has industry experience in robotics and helps students bridge academic knowledge with practical applications.',
      'availability': 'Mondays and Wednesdays, 1-3 PM',
      'rating': 4.6,
      'reviews': 12,
    },
    // Add more mentors as needed
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Find a Mentor')),
      body: _currentStep < 4 ? _buildQuestionnaire() : _buildMentorResults(),
    );
  }

  Widget _buildQuestionnaire() {
    return Stepper(
      currentStep: _currentStep,
      onStepContinue: () {
        setState(() {
          if (_currentStep < 3) {
            _currentStep += 1;
          } else {
            // Last step, show results
            _currentStep = 4;
          }
        });
      },
      onStepCancel: () {
        setState(() {
          if (_currentStep > 0) {
            _currentStep -= 1;
          }
        });
      },
      steps: [
        Step(
          title: const Text('Select Your Program'),
          content: DropdownButtonFormField<String>(
            value: _selectedProgram,
            hint: const Text('Choose your program'),
            isExpanded: true,
            items:
                _programs.map((program) {
                  return DropdownMenuItem(value: program, child: Text(program));
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedProgram = value;
              });
            },
          ),
          isActive: _currentStep >= 0,
        ),
        Step(
          title: const Text('Select Your Year'),
          content: DropdownButtonFormField<String>(
            value: _selectedYear,
            hint: const Text('Choose your year'),
            isExpanded: true,
            items:
                _years.map((year) {
                  return DropdownMenuItem(value: year, child: Text(year));
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedYear = value;
              });
            },
          ),
          isActive: _currentStep >= 1,
        ),
        Step(
          title: const Text('Select Your Interests'),
          content: Column(
            children: [
              const Text('Select all that apply:'),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    _interests.map((interest) {
                      final isSelected = _selectedInterests.contains(interest);
                      return FilterChip(
                        label: Text(interest),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedInterests.add(interest);
                            } else {
                              _selectedInterests.remove(interest);
                            }
                          });
                        },
                      );
                    }).toList(),
              ),
            ],
          ),
          isActive: _currentStep >= 2,
        ),
        Step(
          title: const Text('Select Your Primary Goal'),
          content: Column(
            children:
                _goals.map((goal) {
                  return RadioListTile<String>(
                    title: Text(goal),
                    value: goal,
                    groupValue: _selectedGoal,
                    onChanged: (value) {
                      setState(() {
                        _selectedGoal = value;
                      });
                    },
                  );
                }).toList(),
          ),
          isActive: _currentStep >= 3,
        ),
      ],
      controlsBuilder: (context, details) {
        return Padding(
          padding: const EdgeInsets.only(top: 16.0),
          child: Row(
            children: [
              ElevatedButton(
                onPressed: details.onStepContinue,
                child: Text(_currentStep < 3 ? 'Continue' : 'Find Mentors'),
              ),
              if (_currentStep > 0) ...[
                const SizedBox(width: 8),
                TextButton(
                  onPressed: details.onStepCancel,
                  child: const Text('Back'),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildMentorResults() {
    // Filter mentors based on selections
    List<Map<String, dynamic>> filteredMentors = _mentors;

    // In a real app, you would implement more sophisticated filtering
    if (_selectedProgram == 'Computer Science') {
      filteredMentors =
          _mentors
              .where(
                (mentor) =>
                    mentor['title'].toString().contains('Computer Science'),
              )
              .toList();
    } else if (_selectedProgram == 'Engineering') {
      filteredMentors =
          _mentors
              .where(
                (mentor) => mentor['title'].toString().contains('Engineering'),
              )
              .toList();
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Recommended Mentors',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Based on your ${_selectedProgram ?? ''} program and interests in ${_selectedInterests.join(', ')}',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              const Text(
                'Tap on a mentor to view their profile and request a meeting',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: filteredMentors.length,
            itemBuilder: (context, index) {
              final mentor = filteredMentors[index];
              return Card(
                margin: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue.shade100,
                    child: Text(mentor['name'][0]),
                  ),
                  title: Text(mentor['name']),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(mentor['title']),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.star, size: 16, color: Colors.amber),
                          Text(
                            ' ${mentor['rating']} (${mentor['reviews']} reviews)',
                          ),
                        ],
                      ),
                    ],
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      AppRouter.mentorDetailRoute,
                      arguments: mentor,
                    );
                  },
                ),
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: OutlinedButton(
            onPressed: () {
              setState(() {
                _currentStep = 0;
                _selectedInterests.clear();
                _selectedProgram = null;
                _selectedYear = null;
                _selectedGoal = null;
              });
            },
            child: const Text('Start Over'),
          ),
        ),
      ],
    );
  }
}
