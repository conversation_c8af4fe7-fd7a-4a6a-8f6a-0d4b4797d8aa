import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:konnect/screens/peer_connect_screen.dart';
import 'tabs/academia_tab.dart';
import 'tabs/for_you_tab.dart';
import 'tabs/discover_tab.dart';
import '../widgets/bottom_navigation.dart';
import 'notifications_screen.dart';
import 'profile_screen.dart';
import 'settings_screen.dart';
import 'map_screen.dart';
import 'events_screen.dart';
import 'upskill_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final int _currentIndex = 0;
  bool _hasUnreadNotifications =
      true; // State variable to track unread notifications
  final GlobalKey _profileButtonKey = GlobalKey(); // Key for the profile button

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onBottomNavTap(int index) {
    if (index == _currentIndex) return;

    try {
      switch (index) {
        case 0:
          // Already on home screen
          break;
        case 1:
          // Map
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const MapScreen(currentIndex: 1),
            ),
          );
          break;
        case 2:
          // Events
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const EventsScreen(currentIndex: 2),
            ),
          );
          break;
        case 3:
          // Connect
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const PeerConnectScreen(currentIndex: 3),
            ),
          );
          break;
        case 4:
          // Upskill
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const UpskillScreen(currentIndex: 4),
            ),
          );
          break;
      }
    } catch (e) {
      print('Navigation error: $e');
      // Fallback to named route if direct navigation fails
      final routes = ['/home', '/map', '/events', '/connect', '/upskill'];
      if (index < routes.length) {
        Navigator.pushReplacementNamed(context, routes[index]);
      }
    }
  }

  void _showSettingsMenu() {
    final RenderBox? button =
        _profileButtonKey.currentContext?.findRenderObject() as RenderBox?;
    final RenderBox overlay =
        Navigator.of(context).overlay!.context.findRenderObject() as RenderBox;

    if (button == null) return;

    // Get the position of the button relative to the overlay
    final Offset buttonPosition = button.localToGlobal(
      Offset.zero,
      ancestor: overlay,
    );

    // Calculate position to show menu below and aligned with the right edge of the button
    final RelativeRect position = RelativeRect.fromLTRB(
      buttonPosition.dx +
          button.size.width -
          200, // Align menu's right edge with button's right edge (assuming menu width ~200)
      buttonPosition.dy +
          button.size.height +
          8, // Position below the button with some padding
      overlay.size.width -
          buttonPosition.dx -
          button.size.width, // Right constraint
      overlay.size.height -
          buttonPosition.dy -
          button.size.height -
          8, // Bottom constraint
    );

    showMenu<String>(
      context: context,
      position: position,
      items: [
        const PopupMenuItem<String>(
          value: 'darkMode',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.dark_mode),
              SizedBox(width: 8),
              Text('Dark Mode'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'profile',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [Icon(Icons.person), SizedBox(width: 8), Text('Profile')],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'settings',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.settings),
              SizedBox(width: 8),
              Text('Settings'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'help',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.help),
              SizedBox(width: 8),
              Text('Help & Support'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'logout',
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.logout, color: Colors.red),
              SizedBox(width: 8),
              Text('Logout', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
      elevation: 8.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ).then((value) {
      if (value == null) return;

      switch (value) {
        case 'darkMode':
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Dark mode toggle not implemented yet'),
            ),
          );
          break;
        case 'profile':
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ProfileScreen()),
          );
          break;
        case 'settings':
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SettingsScreen()),
          );
          break;
        case 'help':
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Help & Support not implemented yet')),
          );
          break;
        case 'logout':
          _showLogoutDialog();
          break;
      }
    });
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Logout logic would go here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Logged out successfully')),
                );
                // Navigate to login screen
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  '/login',
                  (route) => false,
                );
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverOverlapAbsorber(
              handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
              sliver: SliverAppBar(
                title: const Text(
                  'Campus Connect',
                  style: TextStyle(
                    color: Color(0xFF2A9D8F),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                pinned: true,
                floating: true,
                forceElevated: innerBoxIsScrolled,
                // Remove the back button
                automaticallyImplyLeading: false,
                actions: [
                  Stack(
                    children: [
                      IconButton(
                        icon: const Icon(
                          Icons.notifications,
                          color: Color(0xFF2A9D8F),
                        ),
                        onPressed: () {
                          // Navigate to notifications
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const NotificationsScreen(),
                            ),
                          ).then((_) {
                            // When returning from notifications screen, mark as read
                            setState(() {
                              _hasUnreadNotifications = false;
                            });
                          });
                        },
                      ),
                      if (_hasUnreadNotifications)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            width: 10,
                            height: 10,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                  IconButton(
                    key: _profileButtonKey, // Add the key here
                    icon: const Icon(
                      Icons.account_circle,
                      color: Color(0xFF2A9D8F),
                    ),
                    onPressed: _showSettingsMenu,
                  ),
                ],
                bottom: TabBar(
                  controller: _tabController,
                  labelColor: const Color(0xFF2A9D8F),
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: const Color(0xFF2A9D8F),
                  tabs: const [
                    Tab(text: 'Academia'),
                    Tab(text: 'For You'),
                    Tab(text: 'Discover'),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: const [
            // Academia Tab
            AcademiaTab(),

            // For You Tab
            ForYouTab(),

            // Discover Tab
            DiscoverTab(),
          ],
        ),
      ),
      // Add bottom navigation bar
      bottomNavigationBar: BottomNavigation(
        currentIndex: _currentIndex,
        onTap: _onBottomNavTap,
      ),
    );
  }
}
