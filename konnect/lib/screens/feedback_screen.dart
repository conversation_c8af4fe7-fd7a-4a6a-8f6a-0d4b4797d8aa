import 'package:flutter/material.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _feedbackController = TextEditingController();
  String _feedbackType = 'Bug Report';
  
  final List<String> _feedbackTypes = [
    'Bug Report',
    'Feature Request',
    'General Feedback',
    'Question',
  ];

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Feedback & Support'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Feedback form
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Send Feedback',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Feedback Type',
                          border: OutlineInputBorder(),
                        ),
                        value: _feedbackType,
                        items: _feedbackTypes.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(type),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _feedbackType = value!;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _feedbackController,
                        decoration: const InputDecoration(
                          labelText: 'Your Feedback',
                          hintText: 'Please describe your issue or suggestion...',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 5,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your feedback';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              // Submit feedback
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Thank you for your feedback!'),
                                ),
                              );
                              _feedbackController.clear();
                            }
                          },
                          child: const Padding(
                            padding: EdgeInsets.all(12.0),
                            child: Text('Submit Feedback'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Contact information
            const Text(
              'Contact Us',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.email),
                    title: const Text('Email'),
                    subtitle: const Text('<EMAIL>'),
                    onTap: () {
                      // Launch email client
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Opening email client...')),
                      );
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.phone),
                    title: const Text('Phone'),
                    subtitle: const Text('+****************'),
                    onTap: () {
                      // Launch phone dialer
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Opening phone dialer...')),
                      );
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.chat),
                    title: const Text('Live Chat'),
                    subtitle: const Text('Available Mon-Fri, 9am-5pm'),
                    onTap: () {
                      // Open live chat
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Live chat coming soon')),
                      );
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // FAQ section
            const Text(
              'Frequently Asked Questions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ExpansionPanelList.radio(
              children: [
                ExpansionPanelRadio(
                  value: 'faq1',
                  headerBuilder: (context, isExpanded) {
                    return const ListTile(
                      title: Text('How do I reset my password?'),
                    );
                  },
                  body: const Padding(
                    padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                    child: Text(
                      'Go to the login screen and tap on "Forgot Password". Follow the instructions sent to your email to reset your password.',
                    ),
                  ),
                ),
                ExpansionPanelRadio(
                  value: 'faq2',
                  headerBuilder: (context, isExpanded) {
                    return const ListTile(
                      title: Text('How do I join a club?'),
                    );
                  },
                  body: const Padding(
                    padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                    child: Text(
                      'Navigate to the Connect tab, select Clubs, and find a club you\'re interested in. Tap on it to view details and tap the "Join Club" button.',
                    ),
                  ),
                ),
                ExpansionPanelRadio(
                  value: 'faq3',
                  headerBuilder: (context, isExpanded) {
                    return const ListTile(
                      title: Text('How do I find a mentor?'),
                    );
                  },
                  body: const Padding(
                    padding: EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
                    child: Text(
                      'Go to the Connect tab, select Mentorship, fill out your profile information, and tap "Find My Mentor" to get matched with suitable mentors.',
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}