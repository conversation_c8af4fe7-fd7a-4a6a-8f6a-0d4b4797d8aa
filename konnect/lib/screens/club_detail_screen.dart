import 'package:flutter/material.dart';

class ClubDetailScreen extends StatelessWidget {
  final Map<String, dynamic> clubData;

  const ClubDetailScreen({super.key, required this.clubData});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(clubData['name'] ?? 'Club Details'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              clubData['name'] ?? 'Club Name',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              '${clubData['category'] ?? 'Category'} • ${clubData['members'] ?? '0'} members',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'About',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(clubData['description'] ?? 'No description available.'),
            const SizedBox(height: 16),
            Text(
              'Meeting Times',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(clubData['meetingTimes'] ?? 'Not specified'),
            const SizedBox(height: 16),
            Text(
              'Location',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(clubData['location'] ?? 'Not specified'),
            const SizedBox(height: 16),
            Text(
              'Contact',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(clubData['contact'] ?? 'Not specified'),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Join club logic
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Request to join sent!')),
                  );
                },
                child: const Text('Join Club'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
