import 'package:flutter/material.dart';
import 'package:calendar_timeline/calendar_timeline.dart';
import 'package:timeline_tile/timeline_tile.dart';
import '../services/timetable_service.dart';
import '../widgets/bottom_navigation.dart';
import '../utils/skeleton_theme.dart';
import 'map_screen.dart';
import 'events_screen.dart';
import 'peer_connect_screen.dart';
import 'upskill_screen.dart';

class TimetableScreen extends StatefulWidget {
  final bool showNavBar;
  final int currentIndex;
  
  const TimetableScreen({
    super.key,
    this.showNavBar = true,
    this.currentIndex = -1,   // Use -1 to indicate it's not in the bottom nav
  });

  @override
  State<TimetableScreen> createState() => _TimetableScreenState();
}

class _TimetableScreenState extends State<TimetableScreen> {
  List<Map<String, dynamic>> _classes = [];
  DateTime? _selectedDate = DateTime.now();
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadClasses();
  }

  Future<void> _loadClasses() async {
    if (!mounted) return; // Check if widget is still mounted before starting
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      if (_selectedDate == null) {
        // Load all classes
        final classes = await TimetableService.getAllClasses();
        if (!mounted) return; // Check if widget is still mounted after async operation
        setState(() {
          _classes = classes;
          _isLoading = false;
        });
      } else {
        // Load classes for selected date
        final classes = await TimetableService.getClassesByDate(_selectedDate!);
        if (!mounted) return; // Check if widget is still mounted after async operation
        setState(() {
          _classes = classes;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted after error
      setState(() {
        // Store error but don't display it directly to user
        _error = e.toString();
        _isLoading = false;
        // Classes will be empty, showing the "No classes" message
      });
      
      // Show a user-friendly snackbar instead of error text
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to load classes. Pull down to try again.'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // Check if a class is currently in session
  bool _isCurrentClass(String startTime, String endTime) {
    final now = TimeOfDay.now();
    final nowMinutes = now.hour * 60 + now.minute;
    
    final start = _parseTimeString(startTime);
    final end = _parseTimeString(endTime);
    
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;
    
    return nowMinutes >= startMinutes && nowMinutes <= endMinutes;
  }
  
  // Parse time string (e.g., "09:00") to TimeOfDay
  TimeOfDay _parseTimeString(String timeStr) {
    final parts = timeStr.split(':');
    if (parts.length != 2) return const TimeOfDay(hour: 0, minute: 0);
    
    return TimeOfDay(
      hour: int.tryParse(parts[0]) ?? 0,
      minute: int.tryParse(parts[1]) ?? 0,
    );
  }

  // Get color based on subject code
  Color _getSubjectColor(String code) {
    if (code.startsWith('CS') || code.startsWith('cs')) {
      return Colors.blue[700]!;
    } else if (code.startsWith('MATH') || code.startsWith('math')) {
      return Colors.red[600]!;
    } else if (code.startsWith('ENG') || code.startsWith('eng')) {
      return Colors.purple[600]!;
    } else if (code.startsWith('PHYS') || code.startsWith('phys')) {
      return Colors.orange[600]!;
    } else if (code.startsWith('CHEM') || code.startsWith('chem')) {
      return Colors.green[600]!;
    } else if (code.startsWith('BIO') || code.startsWith('bio')) {
      return Colors.teal[600]!;
    } else if (code.startsWith('PSYC') || code.startsWith('psyc')) {
      return Colors.pink[400]!;
    } else {
      return Colors.grey[700]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Class Schedule'),
        backgroundColor: const Color(0xFF2A9D8F),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddClassDialog(),
          ),
        ],
      ),
      bottomNavigationBar: widget.showNavBar
          ? BottomNavigation(  // Changed from KonnectBottomNavigation
              currentIndex: widget.currentIndex,
              onTap: _onBottomNavTap,
            )
          : null,
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          // Calendar timeline
          Container(
            color: Colors.white,
            child: CalendarTimeline(
              initialDate: _selectedDate ?? DateTime.now(),
              firstDate: DateTime.now().subtract(const Duration(days: 30)),
              lastDate: DateTime.now().add(const Duration(days: 365)),
              onDateSelected: (date) {
                setState(() {
                  _selectedDate = date;
                });
                _loadClasses();
              },
              leftMargin: 20,
              monthColor: Colors.grey[600],
              dayColor: const Color(0xFF2A9D8F),
              activeDayColor: Colors.white,
              activeBackgroundDayColor: const Color(0xFF2A9D8F),
              dotColor: const Color(0xFF333A47),
              locale: 'en_ISO',
            ),
          ),
          
          // Class list with RefreshIndicator
          Expanded(
            child: RefreshIndicator(
              onRefresh: _loadClasses,
              child: _isLoading
                ? SkeletonTheme.create(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16.0),
                      itemCount: 5, // Show 5 skeleton items
                      itemBuilder: (context, index) {
                        return Container(
                          margin: const EdgeInsets.only(bottom: 8.0),
                          child: TimelineTile(
                            alignment: TimelineAlign.manual,
                            lineXY: 0.05,
                            isFirst: index == 0,
                            isLast: index == 4, // Last of 5 items
                            indicatorStyle: IndicatorStyle(
                              width: 12,
                              height: 12,
                              indicator: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: const Color(0xFF2A9D8F),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                            beforeLineStyle: const LineStyle(
                              color: Color(0xFFB2DFDB),
                              thickness: 2,
                            ),
                            afterLineStyle: const LineStyle(
                              color: Color(0xFFB2DFDB),
                              thickness: 2,
                            ),
                            endChild: Card(
                              margin: const EdgeInsets.only(left: 8.0, right: 0, bottom: 8.0),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                side: BorderSide(
                                  color: Colors.grey.withOpacity(0.2),
                                  width: 1.0,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 10.0, 
                                            vertical: 4.0,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.grey[200],
                                            borderRadius: BorderRadius.circular(8.0),
                                          ),
                                          child: const Text(
                                            "CS101",
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 14.0,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    
                                    const SizedBox(height: 12.0),
                                    
                                    const Text(
                                      "Introduction to Computer Science",
                                      style: TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    
                                    const SizedBox(height: 12.0),
                                    
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.access_time,
                                                size: 16.0,
                                                color: Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4.0),
                                              const Text(
                                                "09:00 - 11:00",
                                                style: TextStyle(
                                                  fontSize: 14.0,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        
                                        Expanded(
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.location_on_outlined,
                                                size: 16.0,
                                                color: Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4.0),
                                              const Expanded(
                                                child: Text(
                                                  "Room 101",
                                                  style: TextStyle(
                                                    fontSize: 14.0,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    
                                    const SizedBox(height: 8.0),
                                    
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.person_outline,
                                          size: 16.0,
                                          color: Colors.grey[600],
                                        ),
                                        const SizedBox(width: 4.0),
                                        const Text(
                                          "Dr. John Smith",
                                          style: TextStyle(
                                            fontSize: 14.0,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  )
                : _classes.isEmpty
                  ? ListView(
                      // Need ListView for RefreshIndicator to work with empty content
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                          child: TimelineTile(
                            alignment: TimelineAlign.manual,
                            lineXY: 0.05,
                            isFirst: true,
                            isLast: true,
                            indicatorStyle: IndicatorStyle(
                              width: 12,
                              height: 12,
                              indicator: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.grey[400],
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                            endChild: Card(
                              margin: const EdgeInsets.only(left: 8.0, right: 0, bottom: 8.0),
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12.0),
                                side: BorderSide(
                                  color: Colors.grey.withOpacity(0.2),
                                  width: 1.0,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(24.0),
                                child: Center(
                                  child: Column(
                                    children: [
                                      Icon(Icons.event_available, size: 48, color: Colors.grey[400]),
                                      const SizedBox(height: 16),
                                      const Text(
                                        'No classes for this day!',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF757575),
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'Enjoy your free time',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Color(0xFF9E9E9E),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16.0),
                      itemCount: _classes.length,
                      itemBuilder: (context, index) {
                        final classItem = _classes[index];
                        
                        // Extract course code from class name
                        final courseCode = classItem['code'] ?? classItem['name'].split(':').first.trim();
                        final courseName = classItem['name'];
                        
                        // Check if this is the current class
                        final isCurrentClass = _isCurrentClass(
                          classItem['startTime'], 
                          classItem['endTime']
                        );
                        
                        // Get the color for the timeline and indicators
                        final timelineColor = isCurrentClass ? Colors.green : const Color(0xFF2A9D8F);
                        
                        return Container(
                          margin: const EdgeInsets.only(bottom: 8.0),
                          child: TimelineTile(
                            alignment: TimelineAlign.manual,
                            lineXY: 0.05, // Position line at 5% from left
                            isFirst: index == 0,
                            isLast: index == _classes.length - 1,
                            indicatorStyle: IndicatorStyle(
                              width: 12,
                              height: 12,
                              indicator: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: isCurrentClass ? Colors.green : const Color(0xFF2A9D8F),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 2,
                                  ),
                                ),
                                child: isCurrentClass 
                                    ? const Center(
                                        child: Icon(
                                          Icons.play_arrow,
                                          size: 8,
                                          color: Colors.white,
                                        ),
                                      )
                                    : null,
                              ),
                            ),
                            beforeLineStyle: LineStyle(
                              color: timelineColor.withOpacity(0.3),
                              thickness: 2,
                            ),
                            afterLineStyle: LineStyle(
                              color: timelineColor.withOpacity(0.3),
                              thickness: 2,
                            ),
                            endChild: InkWell(
                              onTap: () => _showClassDetails(classItem),
                              child: Card(
                                margin: const EdgeInsets.only(left: 8.0, right: 0, bottom: 8.0),
                                elevation: 0, // Remove elevation/shadow
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.0),
                                  side: BorderSide(
                                    color: isCurrentClass 
                                        ? Colors.green.withOpacity(0.5) 
                                        : Colors.grey.withOpacity(0.2),
                                    width: isCurrentClass ? 2.0 : 1.0,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          // Course code with colored background
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 10.0, 
                                              vertical: 4.0,
                                            ),
                                            decoration: BoxDecoration(
                                              color: _getSubjectColor(courseCode.toString()).withOpacity(0.1),
                                              borderRadius: BorderRadius.circular(8.0),
                                            ),
                                            child: Text(
                                              courseCode.toString(),
                                              style: TextStyle(
                                                color: _getSubjectColor(courseCode.toString()),
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14.0,
                                              ),
                                            ),
                                          ),
                                          
                                          // Current class indicator
                                          if (isCurrentClass)
                                            Container(
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 10.0, 
                                                vertical: 4.0,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.green.withOpacity(0.1),
                                                borderRadius: BorderRadius.circular(8.0),
                                              ),
                                              child: Row(
                                                children: [
                                                  Container(
                                                    width: 8,
                                                    height: 8,
                                                    decoration: const BoxDecoration(
                                                      color: Colors.green,
                                                      shape: BoxShape.circle,
                                                    ),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  const Text(
                                                    'In Progress',
                                                    style: TextStyle(
                                                      color: Colors.green,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: 12.0,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                        ],
                                      ),
                                      
                                      const SizedBox(height: 12.0),
                                      
                                      // Course name
                                      Text(
                                        courseName.toString(),
                                        style: const TextStyle(
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      
                                      const SizedBox(height: 12.0),
                                      
                                      // Class details in a row
                                      Row(
                                        children: [
                                          // Time
                                          Expanded(
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.access_time,
                                                  size: 16.0,
                                                  color: Colors.grey[600],
                                                ),
                                                const SizedBox(width: 4.0),
                                                Text(
                                                  '${classItem['startTime']} - ${classItem['endTime']}',
                                                  style: TextStyle(
                                                    color: Colors.grey[800],
                                                    fontSize: 14.0,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          
                                          // Location
                                          Expanded(
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.location_on_outlined,
                                                  size: 16.0,
                                                  color: Colors.grey[600],
                                                ),
                                                const SizedBox(width: 4.0),
                                                Expanded(
                                                  child: Text(
                                                    classItem['location'] ?? 'Unknown Location',
                                                    style: TextStyle(
                                                      color: Colors.grey[800],
                                                      fontSize: 14.0,
                                                    ),
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      
                                      const SizedBox(height: 8.0),
                                      
                                      // Professor
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.person_outline,
                                            size: 16.0,
                                            color: Colors.grey[600],
                                          ),
                                          const SizedBox(width: 4.0),
                                          Text(
                                            classItem['professor'] ?? 'Unknown Instructor',
                                            style: TextStyle(
                                              color: Colors.grey[800],
                                              fontSize: 14.0,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddClassDialog,
        backgroundColor: const Color(0xFF2A9D8F),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, Color color, [VoidCallback? onTap]) {
    return InkWell(
      onTap: onTap ?? () {},
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  int _min(int a, int b) {
    return a < b ? a : b;
  }

  List<Map<String, dynamic>> _getClassesForSelectedDate() {
    if (_selectedDate == null) {
      // Return all classes when in "View All" mode
      return _classes;
    }
    
    // Convert day name from DateTime
    final dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    final dayName = dayNames[_selectedDate!.weekday - 1]; // weekday is 1-7 where 1 is Monday
    
    return _classes
        .where((classItem) => classItem['day'] == dayName)
        .toList();
  }

  void _onBottomNavTap(int index) {
    if (index == widget.currentIndex) return;
    
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/home');
        break;
      case 1:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const MapScreen(showNavBar: true, currentIndex: 1)),
        );
        break;
      case 2:
        // Already on timetable
        break;
      case 3:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const EventsScreen(showNavBar: true, currentIndex: 3)),
        );
        break;
      case 4:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const PeerConnectScreen(showNavBar: true, currentIndex: 4)),
        );
        // Or use named route:
        // Navigator.pushReplacementNamed(context, AppRouter.connectRoute);
        break;
      case 5:
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const UpskillScreen(showNavBar: true, currentIndex: 5)),
        );
        break;
    }
  }

  int _getOriginalIndex(Map<String, dynamic> classItem) {
    return _classes.indexWhere(
      (c) =>
          c['name'] == classItem['name'] &&
          c['day'] == classItem['day'] &&
          c['startTime'] == classItem['startTime'],
    );
  }

  void _showClassDetails(Map<String, dynamic> classItem) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  classItem['name'],
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  classItem['code'] ?? '',
                  style: TextStyle(
                    fontSize: 18,
                    color: _getSubjectColor(classItem['code'] ?? ''),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.access_time),
                    const SizedBox(width: 8),
                    Text(
                      '${classItem['day']}, ${classItem['startTime']} - ${classItem['endTime']}',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.location_on),
                    const SizedBox(width: 8),
                    Text(
                      classItem['location'] ?? 'TBA',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.person),
                    const SizedBox(width: 8),
                    Text(
                      classItem['professor'] ?? 'TBA',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Edit class
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit'),
                    ),
                    OutlinedButton.icon(
                      onPressed: () {
                        final index = _getOriginalIndex(classItem);
                        _toggleReminder(index);
                        Navigator.pop(context);
                      },
                      icon: Icon(
                        classItem['reminders'] == true
                            ? Icons.notifications_off
                            : Icons.notifications_active,
                      ),
                      label: Text(
                        classItem['reminders'] == true
                            ? 'Remove Reminder'
                            : 'Set Reminder',
                      ),
                    ),
                    OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Delete Class'),
                            content: const Text('Are you sure you want to delete this class?'),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('CANCEL'),
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _deleteClass(classItem['id']);
                                },
                                child: const Text('DELETE', style: TextStyle(color: Colors.red)),
                              ),
                            ],
                          ),
                        );
                      },
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text('Delete', style: TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  void _toggleReminder(int index) async {
    try {
      final updatedClass = Map<String, dynamic>.from(_classes[index]);
      updatedClass['reminders'] = !updatedClass['reminders'];
      
      // Update in the backend
      // The updateClass method expects an ID and a class object
      await TimetableService.updateClass(updatedClass['id'], updatedClass);
      
      // Check if widget is still mounted before updating state
      if (!mounted) return;
      
      setState(() {
        _classes[index] = updatedClass;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              updatedClass['reminders']
                  ? 'Reminders enabled'
                  : 'Reminders disabled',
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating reminder: $e'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showAddClassDialog() {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final codeController = TextEditingController();
    final locationController = TextEditingController();
    final professorController = TextEditingController();
    String selectedDay = 'Monday';
    TimeOfDay startTime = const TimeOfDay(hour: 9, minute: 0);
    TimeOfDay endTime = const TimeOfDay(hour: 10, minute: 30);
    bool reminders = false;

    String formatTimeOfDay(TimeOfDay time) {
      final hour = time.hour.toString().padLeft(2, '0');
      final minute = time.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Class'),
        content: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(labelText: 'Class Name'),
                  validator: (value) => value!.isEmpty ? 'Required' : null,
                ),
                TextFormField(
                  controller: codeController,
                  decoration: const InputDecoration(labelText: 'Class Code'),
                  validator: (value) => value!.isEmpty ? 'Required' : null,
                ),
                DropdownButtonFormField<String>(
                  value: selectedDay,
                  decoration: const InputDecoration(labelText: 'Day'),
                  items: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
                      .map((day) => DropdownMenuItem(value: day, child: Text(day)))
                      .toList(),
                  onChanged: (value) {
                    selectedDay = value!;
                  },
                ),
                ListTile(
                  title: const Text('Start Time'),
                  subtitle: Text(formatTimeOfDay(startTime)),
                  trailing: const Icon(Icons.access_time),
                  onTap: () async {
                    final picked = await showTimePicker(
                      context: context,
                      initialTime: startTime,
                    );
                    if (picked != null) {
                      startTime = picked;
                    }
                  },
                ),
                ListTile(
                  title: const Text('End Time'),
                  subtitle: Text(formatTimeOfDay(endTime)),
                  trailing: const Icon(Icons.access_time),
                  onTap: () async {
                    final picked = await showTimePicker(
                      context: context,
                      initialTime: endTime,
                    );
                    if (picked != null) {
                      endTime = picked;
                    }
                  },
                ),
                TextFormField(
                  controller: locationController,
                  decoration: const InputDecoration(labelText: 'Location'),
                  validator: (value) => value!.isEmpty ? 'Required' : null,
                ),
                TextFormField(
                  controller: professorController,
                  decoration: const InputDecoration(labelText: 'Professor'),
                  validator: (value) => value!.isEmpty ? 'Required' : null,
                ),
                SwitchListTile(
                  title: const Text('Enable Reminders'),
                  value: reminders,
                  onChanged: (value) {
                    reminders = value;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                final newClass = {
                  'name': nameController.text,
                  'code': codeController.text,
                  'day': selectedDay,
                  'startTime': formatTimeOfDay(startTime),
                  'endTime': formatTimeOfDay(endTime),
                  'location': locationController.text,
                  'professor': professorController.text,
                  'reminders': reminders,
                };

                try {
                  await TimetableService.addClass(newClass);
                  Navigator.pop(context);
                  _loadClasses(); // Reload classes after adding
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Class added successfully')),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error adding class: $e')),
                  );
                }
              }
            },
            child: const Text('ADD'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteClass(int id) async {
    try {
      await TimetableService.deleteClass(id);
      _loadClasses(); // Reload classes after deletion
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Class deleted successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting class: $e')),
      );
    }
  }

  // Add dispose method to clean up any resources
  @override
  void dispose() {
    // Cancel any pending timers or animation callbacks here
    // For example, if you have any Timer objects:
    // _timer?.cancel();
    
    super.dispose();
  }
}
