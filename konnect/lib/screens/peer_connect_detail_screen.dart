import 'package:flutter/material.dart';

class PeerConnectDetailScreen extends StatelessWidget {
  final Map<String, dynamic> peerData;

  const PeerConnectDetailScreen({super.key, required this.peerData});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(peerData['name'] ?? 'Peer Details'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: CircleAvatar(
                radius: 50,
                child: Text(
                  (peerData['name'] as String? ?? 'P')[0],
                  style: const TextStyle(fontSize: 40),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: Text(
                peerData['name'] ?? 'Peer Name',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
            ),
            Center(
              child: Text(
                '${peerData['program'] ?? 'Program'} • ${peerData['year'] ?? 'Year'}',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'Bio',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(peerData['bio'] ?? 'No bio available.'),
            const SizedBox(height: 16),
            Text(
              'Interests',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: (peerData['interests'] as List<dynamic>? ?? [])
                  .map((e) => Chip(label: Text(e.toString())))
                  .toList(),
            ),
            const SizedBox(height: 16),
            Text(
              'Courses',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: (peerData['courses'] as List<dynamic>? ?? [])
                  .map((e) => Chip(label: Text(e.toString())))
                  .toList(),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Connect logic
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Connection request sent!')),
                  );
                },
                child: Text(peerData['connectionStatus'] ?? 'Connect'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
