import 'package:flutter/material.dart';
import 'package:konnect/screens/home_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  OnboardingScreenState createState() => OnboardingScreenState();
}

class OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Updated onboarding data with 4 screens
  final List<Map<String, String>> _onboardingData = [
    {
      'image': 'assets/images/onboarding1.png',
      'title': 'Welcome to Konnect',
      'description':
          'Your personalized guide to campus life at Chuka University.',
    },
    {
      'image': 'assets/images/onboarding2.png',
      'title': 'Connect with <PERSON><PERSON>',
      'description':
          'Find and connect with fellow students and faculty members easily.',
    },
    {
      'image': 'assets/images/onboarding3.png',
      'title': 'Access Resources',
      'description':
          'Get quick access to academic resources and campus facilities.',
    },
    {
      'image': 'assets/images/onboarding1.png', // You'll need a 4th image
      'title': 'Stay Updated',
      'description':
          'Receive timely notifications about campus events and announcements.',
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Full-screen PageView
          PageView.builder(
            controller: _pageController,
            itemCount: _onboardingData.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              return _buildPage(index);
            },
          ),

          // Bottom controls
          Positioned(
            bottom: 30,
            left: 20,
            right: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Indicators moved to bottom left and enlarged
                _buildIndicator(),

                // Next/Get Started button
                _buildButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPage(int index) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Image covering the entire screen
        Image.asset(_onboardingData[index]['image']!, fit: BoxFit.cover),

        // Gradient overlay for better text visibility
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.transparent, Colors.teal.withOpacity(0.8)],
              stops: const [0.6, 1.0],
            ),
          ),
        ),

        // Text content at the bottom
        Positioned(
          bottom: 120,
          left: 20,
          right: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _onboardingData[index]['title']!,
                style: const TextStyle(
                  fontSize: 32, // Larger font for title
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _onboardingData[index]['description']!,
                style: const TextStyle(
                  fontSize: 18, // Larger font for description
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIndicator() {
    return Row(
      children: List.generate(
        _onboardingData.length,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          height: 10,
          width: _currentPage == index ? 25 : 10, // Expanding effect
          decoration: BoxDecoration(
            color:
                _currentPage == index
                    ? Colors.white
                    : Colors.white.withOpacity(0.5),
            borderRadius: BorderRadius.circular(5),
          ),
        ),
      ),
    );
  }

  Widget _buildButton() {
    return ElevatedButton(
      onPressed: () {
        if (_currentPage == _onboardingData.length - 1) {
          // Navigate to auth screen when on the last page
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        } else {
          // Go to next page
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeIn,
          );
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Colors.teal,
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(30)),
      ),
      child: Text(
        _currentPage == _onboardingData.length - 1 ? 'Get Started' : 'Next',
        style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
      ),
    );
  }
}
