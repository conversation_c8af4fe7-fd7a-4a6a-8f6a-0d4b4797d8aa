import 'package:flutter/material.dart';
import '../routes/app_router.dart';

class ClubsScreen extends StatefulWidget {
  const ClubsScreen({super.key});

  @override
  State<ClubsScreen> createState() => _ClubsScreenState();
}

class _ClubsScreenState extends State<ClubsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _categories = [
    'All',
    'Arts',
    'Tech',
    'Sports',
    'Academic',
    'Cultural',
  ];

  final List<Map<String, dynamic>> _clubs = [
    {
      'name': 'Tech Innovators',
      'category': 'Tech',
      'members': 45,
      'description':
          'A club for tech enthusiasts to collaborate on innovative projects',
      'meetingTimes': 'Tuesdays, 5:00 PM',
      'location': 'Engineering Building, Room 201',
      'contact': '<EMAIL>',
    },
    {
      'name': 'Drama Society',
      'category': 'Arts',
      'members': 32,
      'description':
          'Express yourself through the art of drama and performance',
      'meetingTimes': 'Mondays & Thursdays, 6:00 PM',
      'location': 'Arts Center, Theater Room',
      'contact': '<EMAIL>',
    },
    // Add more clubs as needed
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Clubs & Societies'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _categories.map((category) => Tab(text: category)).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children:
            _categories.map((category) {
              final filteredClubs =
                  category == 'All'
                      ? _clubs
                      : _clubs
                          .where((club) => club['category'] == category)
                          .toList();

              return ListView.builder(
                itemCount: filteredClubs.length,
                itemBuilder: (context, index) {
                  final club = filteredClubs[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 8.0,
                    ),
                    child: ListTile(
                      leading: CircleAvatar(child: Text(club['name'][0])),
                      title: Text(club['name']),
                      subtitle: Text(
                        '${club['category']} • ${club['members']} members',
                      ),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          AppRouter.clubDetailRoute,
                          arguments: club,
                        );
                      },
                    ),
                  );
                },
              );
            }).toList(),
      ),
    );
  }
}
