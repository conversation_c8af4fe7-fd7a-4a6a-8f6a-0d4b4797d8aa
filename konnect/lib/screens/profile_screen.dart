import 'package:flutter/material.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final Map<String, dynamic> _userData = {
    'name': '<PERSON>',
    'email': '<EMAIL>',
    'studentId': '1234567',
    'program': 'Computer Science',
    'year': 'Second Year',
    'gpa': '3.8',
    'profileImage': 'assets/images/profile.jpg',
  };

  final List<Map<String, dynamic>> _achievements = [
    {
      'title': 'Dean\'s List',
      'description': 'Fall Semester 2022',
      'icon': Icons.star,
    },
    {
      'title': 'Hackathon Winner',
      'description': 'University Tech Challenge 2023',
      'icon': Icons.emoji_events,
    },
    {
      'title': 'Research Assistant',
      'description': 'AI Lab - Dr. Smith',
      'icon': Icons.science,
    },
  ];

  final List<Map<String, dynamic>> _activities = [
    {
      'title': 'Programming Club',
      'role': 'Member',
      'icon': Icons.code,
    },
    {
      'title': 'Student Government',
      'role': 'Committee Member',
      'icon': Icons.groups,
    },
    {
      'title': 'Chess Club',
      'role': 'Treasurer',
      'icon': Icons.casino,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // Navigate to settings
              Navigator.pushNamed(context, '/settings');
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header
            _buildProfileHeader(),
            
            // Academic Information
            _buildSection('Academic Information', Icons.school, _buildAcademicInfo()),
            
            // Achievements
            _buildSection('Achievements', Icons.emoji_events, _buildAchievements()),
            
            // Activities
            _buildSection('Activities & Clubs', Icons.groups, _buildActivities()),
            
            // Account Actions
            _buildSection('Account', Icons.account_circle, _buildAccountActions()),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: const Color(0xFF2A9D8F).withOpacity(0.1),
      ),
      child: Column(
        children: [
          // Profile Image
          CircleAvatar(
            radius: 50,
            backgroundColor: const Color(0xFF2A9D8F),
            child: const Icon(
              Icons.person,
              size: 50,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          
          // Name
          Text(
            _userData['name'],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF264653),
            ),
          ),
          const SizedBox(height: 8),
          
          // Program and Year
          Text(
            '${_userData['program']} - ${_userData['year']}',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 16),
          
          // Quick Stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStat('GPA', _userData['gpa']),
              _buildStat('Courses', '5'),
              _buildStat('Credits', '15'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2A9D8F),
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: const Color(0xFF2A9D8F)),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF264653),
                  ),
                ),
              ],
            ),
            const Divider(),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildAcademicInfo() {
    return Column(
      children: [
        _buildInfoRow('Student ID', _userData['studentId']),
        _buildInfoRow('Email', _userData['email']),
        _buildInfoRow('Program', _userData['program']),
        _buildInfoRow('Year', _userData['year']),
        _buildInfoRow('GPA', _userData['gpa']),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievements() {
    return Column(
      children: _achievements.map((achievement) {
        return ListTile(
          leading: Icon(
            achievement['icon'] as IconData,
            color: const Color(0xFF2A9D8F),
          ),
          title: Text(
            achievement['title'] as String,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Text(achievement['description'] as String),
        );
      }).toList(),
    );
  }

  Widget _buildActivities() {
    return Column(
      children: _activities.map((activity) {
        return ListTile(
          leading: Icon(
            activity['icon'] as IconData,
            color: const Color(0xFF2A9D8F),
          ),
          title: Text(
            activity['title'] as String,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          subtitle: Text(activity['role'] as String),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () {},
        );
      }).toList(),
    );
  }

  Widget _buildAccountActions() {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.edit, color: Color(0xFF2A9D8F)),
          title: const Text('Edit Profile'),
          onTap: () {},
        ),
        ListTile(
          leading: const Icon(Icons.privacy_tip, color: Color(0xFF2A9D8F)),
          title: const Text('Privacy Settings'),
          onTap: () {},
        ),
        ListTile(
          leading: const Icon(Icons.help, color: Color(0xFF2A9D8F)),
          title: const Text('Help & Support'),
          onTap: () {},
        ),
        ListTile(
          leading: const Icon(Icons.logout, color: Colors.red),
          title: const Text('Logout', style: TextStyle(color: Colors.red)),
          onTap: () {
            // Show logout confirmation
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Logout'),
                content: const Text('Are you sure you want to logout?'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // Perform logout
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Logged out successfully')),
                      );
                      // Navigate to login screen
                      Navigator.pushNamedAndRemoveUntil(
                        context, 
                        '/login', 
                        (route) => false,
                      );
                    },
                    child: const Text('Logout'),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}
