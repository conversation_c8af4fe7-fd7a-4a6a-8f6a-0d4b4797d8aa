import 'package:flutter/material.dart';
import 'package:konnect/routes/app_router.dart';
import 'package:konnect/screens/home_screen.dart';
import 'package:konnect/screens/map_screen.dart';
import 'package:konnect/screens/peer_connect_screen.dart';
import 'package:konnect/screens/upskill_screen.dart';
import 'package:konnect/widgets/bottom_navigation.dart';
import 'dart:async';
import 'package:konnect/screens/event_detail_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';

class EventsScreen extends StatefulWidget {
  final bool showNavBar;
  final int currentIndex;

  const EventsScreen({
    super.key,
    this.showNavBar = true,
    this.currentIndex = 2,
  });

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  late PageController _featuredPageController;
  Timer? _autoPlayTimer; // Make nullable to avoid late initialization
  int _currentFeaturedIndex = 0;
  final bool _isLoading = false; // Remove initial loading state

  // Add animation controller for smooth transitions
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Search variables
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchActive = false;
  List<EventData> _searchResults = [];
  String _currentSearchQuery = '';
  Timer? _searchDebounce;

  // Cache for search results to avoid recomputation
  final Map<String, List<EventData>> _searchCache = {};

  // Lazy-loaded all events list
  List<EventData>? _allEvents;

  // Define the featured events data
  final List<EventData> featuredEvents = [
    EventData(
      title: 'Tech Conference 2023',
      subtitle: 'Main Campus Auditorium',
      description:
          'Join us for the biggest tech event of the year with industry leaders and workshops.',
      date: 15,
      month: 'Oct',
      backgroundImage:
          'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      avatars: [
        'https://randomuser.me/api/portraits/men/32.jpg',
        'https://randomuser.me/api/portraits/women/44.jpg',
        'https://randomuser.me/api/portraits/men/78.jpg',
      ],
      isFree: false,
      price: 25.0,
    ),
    EventData(
      title: 'Career Fair',
      subtitle: 'Business School Building',
      description:
          'Connect with top employers and explore internship and job opportunities.',
      date: 22,
      month: 'Oct',
      backgroundImage:
          'https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80',
      avatars: [
        'https://randomuser.me/api/portraits/women/68.jpg',
        'https://randomuser.me/api/portraits/men/43.jpg',
      ],
      isFree: true,
    ),
    EventData(
      title: 'Hackathon 2023',
      subtitle: 'Engineering Building',
      description:
          '48-hour coding challenge with prizes and networking opportunities.',
      date: 5,
      month: 'Nov',
      backgroundImage:
          'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      avatars: [
        'https://randomuser.me/api/portraits/men/32.jpg',
        'https://randomuser.me/api/portraits/women/44.jpg',
        'https://randomuser.me/api/portraits/men/78.jpg',
        'https://randomuser.me/api/portraits/women/68.jpg',
      ],
      isFree: false,
      price: 10.0,
    ),
  ];

  // Define the category events data
  final Map<String, List<EventData>> categoryEvents = {
    'Academic': [
      EventData(
        title: 'Research Symposium',
        subtitle: 'Science Building',
        description:
            'Undergraduate and graduate students present their research projects.',
        date: 18,
        month: 'Oct',
        backgroundImage:
            'https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        avatars: [
          'https://randomuser.me/api/portraits/women/33.jpg',
          'https://randomuser.me/api/portraits/men/91.jpg',
        ],
        isFree: true,
      ),
      EventData(
        title: 'Guest Lecture Series',
        subtitle: 'Liberal Arts Hall',
        description:
            'Distinguished professor from Oxford University discusses modern literature.',
        date: 25,
        month: 'Oct',
        backgroundImage:
            'https://images.unsplash.com/photo-1475721027785-f74eccf877e2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        avatars: ['https://randomuser.me/api/portraits/men/32.jpg'],
        isFree: true,
      ),
    ],
    'Social': [
      EventData(
        title: 'International Food Festival',
        subtitle: 'Student Union',
        description:
            'Taste cuisines from around the world prepared by international students.',
        date: 20,
        month: 'Oct',
        backgroundImage:
            'https://images.unsplash.com/photo-1528605248644-14dd04022da1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        avatars: [
          'https://randomuser.me/api/portraits/women/75.jpg',
          'https://randomuser.me/api/portraits/men/42.jpg',
          'https://randomuser.me/api/portraits/women/62.jpg',
        ],
        isFree: false,
        price: 15.0,
      ),
      EventData(
        title: 'Movie Night',
        subtitle: 'Outdoor Quad',
        description:
            'Outdoor screening of popular movies. Bring your own blankets and snacks!',
        date: 28,
        month: 'Oct',
        backgroundImage:
            'https://images.unsplash.com/photo-1536440136628-849c177e76a1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1025&q=80',
        avatars: [
          'https://randomuser.me/api/portraits/women/45.jpg',
          'https://randomuser.me/api/portraits/men/22.jpg',
        ],
        isFree: true,
      ),
    ],
    'Sports': [
      EventData(
        title: 'Intramural Basketball',
        subtitle: 'Recreation Center',
        description:
            'Sign up with your team for the intramural basketball tournament.',
        date: 12,
        month: 'Nov',
        backgroundImage:
            'https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1090&q=80',
        avatars: [
          'https://randomuser.me/api/portraits/men/67.jpg',
          'https://randomuser.me/api/portraits/women/31.jpg',
        ],
        isFree: false,
        price: 5.0,
      ),
      EventData(
        title: 'Yoga on the Lawn',
        subtitle: 'Main Quad',
        description:
            'Relax and destress with outdoor yoga sessions led by certified instructors.',
        date: 19,
        month: 'Oct',
        backgroundImage:
            'https://images.unsplash.com/photo-1599901860904-17e6ed7083a0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        avatars: ['https://randomuser.me/api/portraits/women/55.jpg'],
        isFree: true,
      ),
    ],
    'Workshops': [
      EventData(
        title: 'Resume Building Workshop',
        subtitle: 'Career Center',
        description:
            'Learn how to create a standout resume with guidance from career counselors.',
        date: 26,
        month: 'Oct',
        backgroundImage:
            'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
        avatars: ['https://randomuser.me/api/portraits/men/61.jpg'],
        isFree: true,
      ),
      EventData(
        title: 'Photography Basics',
        subtitle: 'Arts Building',
        description:
            'Hands-on workshop covering the fundamentals of photography and composition.',
        date: 2,
        month: 'Nov',
        backgroundImage:
            'https://images.unsplash.com/photo-1452587925148-ce544e77e70d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80',
        avatars: [
          'https://randomuser.me/api/portraits/women/29.jpg',
          'https://randomuser.me/api/portraits/men/87.jpg',
        ],
        isFree: false,
        price: 8.0,
      ),
    ],
  };

  @override
  bool get wantKeepAlive => true; // Keep state when switching tabs

  @override
  void initState() {
    super.initState();
    _featuredPageController = PageController();

    // Initialize animation controller
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    // Start fade in animation
    _fadeController.forward();

    // Delay autoplay start to reduce initial load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _startAutoPlay();
      }
    });
  }

  void _startAutoPlay() {
    _autoPlayTimer?.cancel(); // Cancel existing timer
    _autoPlayTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      // Increased interval
      if (!mounted || !_featuredPageController.hasClients) {
        timer.cancel();
        return;
      }

      final nextIndex = (_currentFeaturedIndex + 1) % featuredEvents.length;

      _featuredPageController.animateToPage(
        nextIndex,
        duration: const Duration(milliseconds: 800), // Smoother animation
        curve: Curves.easeInOutCubic,
      );
    });
  }

  @override
  void dispose() {
    _autoPlayTimer?.cancel();
    _featuredPageController.dispose();
    _searchController.dispose();
    _searchDebounce?.cancel();
    _fadeController.dispose();
    super.dispose();
  }

  // Optimize search with caching and debouncing
  void _onSearchChanged(String query) {
    _searchDebounce?.cancel();

    _searchDebounce = Timer(const Duration(milliseconds: 500), () {
      // Increased debounce
      if (!mounted) return;

      setState(() {
        _currentSearchQuery = query;
        if (query.isEmpty) {
          _isSearchActive = false;
          _searchResults.clear();
        } else {
          _isSearchActive = true;
          _performSearch(query);
        }
      });
    });
  }

  void _performSearch(String query) {
    // Check cache first
    if (_searchCache.containsKey(query)) {
      _searchResults = _searchCache[query]!;
      return;
    }

    // Lazy load all events only when needed
    _allEvents ??= _getAllEvents();

    // Filter events
    final results =
        _allEvents!.where((event) {
          final searchLower = query.toLowerCase();
          return event.title.toLowerCase().contains(searchLower) ||
              event.subtitle.toLowerCase().contains(searchLower) ||
              event.description.toLowerCase().contains(searchLower);
        }).toList();

    // Cache results
    _searchCache[query] = results;
    _searchResults = results;
  }

  List<EventData> _getAllEvents() {
    final List<EventData> allEvents = [];
    allEvents.addAll(featuredEvents);
    for (var events in categoryEvents.values) {
      allEvents.addAll(events);
    }
    return allEvents;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Events',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 24),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: false,
      ),
      body: FadeTransition(opacity: _fadeAnimation, child: _buildBody()),
      bottomNavigationBar:
          widget.showNavBar
              ? BottomNavigation(
                currentIndex: widget.currentIndex,
                onTap: _onBottomNavTap,
              )
              : null,
    );
  }

  Widget _buildBody() {
    return CustomScrollView(
      // Use CustomScrollView for better performance
      slivers: [
        // Search Bar
        SliverToBoxAdapter(child: _buildSearchBar()),

        // Content based on search state
        if (_isSearchActive)
          _buildSearchResultsSliver()
        else
          ..._buildMainContentSlivers(),

        // Bottom padding
        const SliverToBoxAdapter(child: SizedBox(height: 80)),
      ],
    );
  }

  List<Widget> _buildMainContentSlivers() {
    return [
      // Featured Section
      SliverToBoxAdapter(child: _buildFeaturedSection()),
      const SliverToBoxAdapter(child: SizedBox(height: 24)),

      // Category Sections
      ...categoryEvents.entries
          .take(3)
          .map(
            (entry) => SliverToBoxAdapter(
              child: _buildCategorySection(entry.key, entry.value),
            ),
          ),
    ];
  }

  Widget _buildSearchResultsSliver() {
    if (_searchResults.isEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No events found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        if (index == 0) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Search Results (${_searchResults.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          );
        }

        final eventIndex = index - 1;
        return Padding(
          padding: const EdgeInsets.only(bottom: 12, left: 16, right: 16),
          child: _buildSearchResultCard(_searchResults[eventIndex]),
        );
      }, childCount: _searchResults.length + 1),
    );
  }

  Widget _buildFeaturedSection() {
    return Column(
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              Text(
                'Featured',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 280,
          child: PageView.builder(
            controller: _featuredPageController,
            onPageChanged: (index) {
              if (mounted) {
                setState(() {
                  _currentFeaturedIndex = index;
                });
              }
            },
            itemCount: featuredEvents.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: OptimizedEventCard(event: featuredEvents[index]),
              );
            },
          ),
        ),
        // Page indicator
        Padding(
          padding: const EdgeInsets.only(top: 12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              featuredEvents.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: _currentFeaturedIndex == index ? 16 : 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color:
                      _currentFeaturedIndex == index
                          ? Colors.blue
                          : Colors.grey[300],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategorySection(String category, List<EventData> events) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              GestureDetector(
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('See all $category events'),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
                child: Text(
                  'See all',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[600],
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 210, // 0.75 of original height (280 * 0.75)
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: events.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 12.0),
                child: SizedBox(
                  width:
                      MediaQuery.of(context).size.width *
                      0.5, // 0.5 of screen width
                  child: CompactEventCard(event: events[index]),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  void _onBottomNavTap(int index) {
    if (index == widget.currentIndex) return;

    try {
      switch (index) {
        case 0:
          Navigator.pushReplacementNamed(context, AppRouter.homeRoute);
          break;
        case 1:
          Navigator.pushReplacementNamed(context, AppRouter.mapRoute);
          break;
        case 2:
          // Already on events screen
          break;
        case 3:
          Navigator.pushReplacementNamed(context, AppRouter.connectRoute);
          break;
        case 4:
          Navigator.pushReplacementNamed(context, AppRouter.upskillRoute);
          break;
      }
    } catch (e) {
      debugPrint('Navigation error: $e');
      _directNavigation(index);
    }
  }

  // Add this helper method for direct navigation as fallback
  void _directNavigation(int index) {
    try {
      switch (index) {
        case 0:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
          break;
        case 1:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const MapScreen(currentIndex: 1),
            ),
          );
          break;
        case 2:
          // Already on events screen
          break;
        case 3:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const PeerConnectScreen(currentIndex: 3),
            ),
          );
          break;
        case 4:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const UpskillScreen(currentIndex: 4),
            ),
          );
          break;
      }
    } catch (e) {
      print('Direct navigation error: $e');
      // Show error to user
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Navigation error: $e')));
    }
  }

  // Build search bar widget
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.search, color: Colors.grey[600], size: 22),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Search events...',
                hintStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          if (_isSearchActive)
            GestureDetector(
              onTap: _clearSearch,
              child: Container(
                padding: const EdgeInsets.all(4),
                child: Icon(Icons.clear, color: Colors.grey[600], size: 20),
              ),
            ),
        ],
      ),
    );
  }

  // Clear search input and reset search state
  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _isSearchActive = false;
      _searchResults.clear();
      _currentSearchQuery = '';
    });
  }

  // Build search results
  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No events found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching with different keywords',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Search Results (${_searchResults.length})',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _searchResults.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildSearchResultCard(_searchResults[index]),
            );
          },
        ),
      ],
    );
  }

  // Build individual search result card
  Widget _buildSearchResultCard(EventData event) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            event.backgroundImage,
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 60,
                height: 60,
                color: Colors.grey[300],
                child: const Icon(
                  Icons.image_not_supported,
                  color: Colors.grey,
                ),
              );
            },
          ),
        ),
        title: Text(
          event.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(event.subtitle),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color:
                (event.isFree ?? false)
                    ? Colors.green.withOpacity(0.9)
                    : Colors.orange.withOpacity(0.9),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            (event.isFree ?? false) ? 'FREE' : 'PAID',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => EventDetailScreen(event: event),
            ),
          );
        },
      ),
    );
  }
}

// Optimized EventCard with better performance
class OptimizedEventCard extends StatelessWidget {
  final EventData event;

  const OptimizedEventCard({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EventDetailScreen(event: event),
          ),
        );
      },
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Optimized background image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: event.backgroundImage,
                  fit: BoxFit.cover,
                  memCacheWidth: 600, // Reduced cache size
                  memCacheHeight: 400,
                  maxWidthDiskCache: 600,
                  maxHeightDiskCache: 400,
                  placeholder:
                      (context, url) => Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2.0),
                          ),
                        ),
                      ),
                  errorWidget:
                      (context, url, error) => Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 48,
                          ),
                        ),
                      ),
                ),
              ),
              // Gradient overlay
              Positioned.fill(
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                      stops: const [0.6, 1.0],
                    ),
                  ),
                ),
              ),
              // Content
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        event.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        event.subtitle,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      // Simplified avatar and description row
                      Row(
                        children: [
                          if (event.avatars.isNotEmpty) ...[
                            _buildAvatarStack(),
                            const SizedBox(width: 8),
                          ],
                          Expanded(
                            child: Text(
                              event.description,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatarStack() {
    final displayCount = event.avatars.length > 3 ? 3 : event.avatars.length;

    return SizedBox(
      height: 24,
      width: displayCount * 16.0 + 8,
      child: Stack(
        children: List.generate(
          displayCount,
          (index) => Positioned(
            left: index * 16.0,
            child: Container(
              height: 24,
              width: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1.5),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CachedNetworkImage(
                  imageUrl: event.avatars[index],
                  fit: BoxFit.cover,
                  memCacheWidth: 48,
                  memCacheHeight: 48,
                  placeholder:
                      (context, url) => Container(color: Colors.grey[300]),
                  errorWidget:
                      (context, error, stackTrace) => Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.person,
                          color: Colors.grey,
                          size: 16,
                        ),
                      ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class CompactEventCard extends StatelessWidget {
  final EventData event;

  const CompactEventCard({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EventDetailScreen(event: event),
          ),
        );
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Stack(
                children: [
                  // Image with loading placeholder
                  CachedNetworkImage(
                    imageUrl: event.backgroundImage,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    memCacheWidth: 400,
                    memCacheHeight: 240,
                    placeholder:
                        (context, url) => Container(
                          height: 120,
                          color: Colors.grey[300],
                          child: const Center(
                            child: CircularProgressIndicator(strokeWidth: 2.0),
                          ),
                        ),
                    errorWidget:
                        (context, url, error) => Container(
                          height: 120,
                          color: Colors.grey[300],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                          ),
                        ),
                  ),
                  // Date overlay
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Text(
                            event.date.toString(),
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            event.month,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Free/Paid tag
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            (event.isFree ?? false)
                                ? Colors.green.withOpacity(0.9)
                                : Colors.orange.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        (event.isFree ?? false) ? 'FREE' : 'PAID',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Event details
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    event.subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // Attendees
                  Row(
                    children: [
                      SizedBox(
                        width: 60,
                        height: 20,
                        child: Stack(
                          children: [
                            for (
                              int i = 0;
                              i < event.avatars.length && i < 3;
                              i++
                            )
                              Positioned(
                                left: i * 20.0,
                                child: CircleAvatar(
                                  radius: 10,
                                  backgroundImage: NetworkImage(
                                    event.avatars[i],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      Text(
                        '${event.avatars.length}+ going',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class EventData {
  final String title;
  final String subtitle;
  final String description;
  final int date;
  final String month;
  final String backgroundImage;
  final List<String> avatars;
  final bool? isFree;
  final double price;

  EventData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.date,
    required this.month,
    required this.backgroundImage,
    required this.avatars,
    this.isFree = false,
    this.price = 0.0,
  });
}
