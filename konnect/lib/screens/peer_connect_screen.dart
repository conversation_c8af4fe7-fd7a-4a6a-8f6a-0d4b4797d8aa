import 'package:flutter/material.dart';
import 'package:konnect/screens/events_screen.dart';
import 'package:konnect/screens/home_screen.dart';
import 'package:konnect/screens/map_screen.dart';
import 'package:konnect/screens/upskill_screen.dart';
import '../routes/app_router.dart';
import '../widgets/bottom_navigation.dart';

class PeerConnectScreen extends StatefulWidget {
  final bool showNavBar;
  final int currentIndex;

  const PeerConnectScreen({
    super.key,
    this.showNavBar = true,
    this.currentIndex = 3,
  });

  @override
  State<PeerConnectScreen> createState() => _PeerConnectScreenState();
}

class _PeerConnectScreenState extends State<PeerConnectScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = ['Clubs', 'Mentorship'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  final List<Map<String, dynamic>> _peers = [
    {
      'name': '<PERSON>',
      'program': 'Computer Science',
      'year': 'Third Year',
      'interests': ['Programming', 'AI', 'Mobile Development'],
      'bio':
          'CS student passionate about AI and mobile app development. Looking for study partners for the upcoming exams.',
      'courses': ['CS301', 'CS405', 'MATH202'],
      'connectionStatus': 'Connect',
    },
    {
      'name': 'Alex Johnson',
      'program': 'Electrical Engineering',
      'year': 'Second Year',
      'interests': ['Robotics', 'IoT', 'Circuit Design'],
      'bio':
          'EE student working on robotics projects. Interested in collaborating on IoT applications.',
      'courses': ['EE201', 'PHYS202', 'MATH201'],
      'connectionStatus': 'Connect',
    },
    {
      'name': 'Taylor Williams',
      'program': 'Business Administration',
      'year': 'Fourth Year',
      'interests': ['Marketing', 'Entrepreneurship', 'Data Analytics'],
      'bio':
          'Business student with a focus on digital marketing. Looking for teammates for the business plan competition.',
      'courses': ['BUS401', 'MKT302', 'FIN301'],
      'connectionStatus': 'Connect',
    },
    {
      'name': 'Jordan Lee',
      'program': 'Psychology',
      'year': 'Third Year',
      'interests': ['Cognitive Psychology', 'Research Methods', 'Statistics'],
      'bio':
          'Psychology major conducting research on learning patterns. Seeking study group for research methods course.',
      'courses': ['PSY301', 'PSY305', 'STAT202'],
      'connectionStatus': 'Connect',
    },
    {
      'name': 'Casey Brown',
      'program': 'Graphic Design',
      'year': 'Second Year',
      'interests': ['UI/UX Design', 'Typography', 'Digital Art'],
      'bio':
          'Design student specializing in UI/UX. Looking for developers to collaborate on app projects.',
      'courses': ['DES201', 'DES205', 'ART202'],
      'connectionStatus': 'Connect',
    },
  ];

  String _searchQuery = '';
  final List<String> _selectedFilters = [];
  final List<String> _availableFilters = [
    'Computer Science',
    'Electrical Engineering',
    'Business Administration',
    'Psychology',
    'Graphic Design',
    'First Year',
    'Second Year',
    'Third Year',
    'Fourth Year',
  ];

  List<Map<String, dynamic>> get _filteredPeers {
    if (_searchQuery.isEmpty && _selectedFilters.isEmpty) {
      return _peers;
    }

    return _peers.where((peer) {
      // Filter by search query
      final matchesSearch =
          _searchQuery.isEmpty ||
          peer['name'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          peer['program'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          peer['bio'].toLowerCase().contains(_searchQuery.toLowerCase());

      // Filter by selected filters
      final matchesFilters =
          _selectedFilters.isEmpty ||
          _selectedFilters.contains(peer['program']) ||
          _selectedFilters.contains(peer['year']);

      return matchesSearch && matchesFilters;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Connect'),
        // Use automatic back button that uses Navigator.pop()
        automaticallyImplyLeading: true,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [_buildClubsTab(), _buildMentorshipTab()],
      ),
      // Always show bottom navigation bar when showNavBar is true
      bottomNavigationBar:
          widget.showNavBar
              ? BottomNavigation(
                currentIndex: 3, // Fixed: Use correct index for Connect tab
                onTap: (index) {
                  // Implement navigation logic here
                  if (index != 3) {
                    // Fixed: Compare with correct index
                    _onBottomNavTap(index);
                  }
                },
              )
              : null,
    );
  }

  Widget _buildPeersTab() {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'Search by name, program, or interests...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 0.0),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),

        // Selected filters
        if (_selectedFilters.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children:
                  _selectedFilters.map((filter) {
                    return Chip(
                      label: Text(filter),
                      onDeleted: () {
                        setState(() {
                          _selectedFilters.remove(filter);
                        });
                      },
                    );
                  }).toList(),
            ),
          ),

        // Peers list
        Expanded(
          child:
              _filteredPeers.isEmpty
                  ? const Center(
                    child: Text('No peers found matching your criteria'),
                  )
                  : ListView.builder(
                    itemCount: _filteredPeers.length,
                    itemBuilder: (context, index) {
                      final peer = _filteredPeers[index];
                      return _buildPeerCard(peer);
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildClubsTab() {
    final List<Map<String, dynamic>> clubs = [
      {
        'name': 'Tech Innovators',
        'category': 'Tech',
        'members': 45,
        'description':
            'A club for tech enthusiasts to collaborate on innovative projects',
        'meetingTimes': 'Tuesdays, 5:00 PM',
        'location': 'Engineering Building, Room 201',
        'contact': '<EMAIL>',
      },
      {
        'name': 'Drama Society',
        'category': 'Arts',
        'members': 32,
        'description':
            'Express yourself through the art of drama and performance',
        'meetingTimes': 'Mondays & Thursdays, 6:00 PM',
        'location': 'Arts Center, Theater Room',
        'contact': '<EMAIL>',
      },
      {
        'name': 'Basketball Club',
        'category': 'Sports',
        'members': 28,
        'description':
            'Join us for regular basketball practice and tournaments',
        'meetingTimes': 'Wednesdays & Saturdays, 4:00 PM',
        'location': 'Sports Complex, Court 2',
        'contact': '<EMAIL>',
      },
    ];

    final List<String> categories = [
      'All',
      'Arts',
      'Tech',
      'Sports',
      'Academic',
      'Cultural',
    ];

    return Column(
      children: [
        // Categories
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  categories.map((category) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4.0),
                      child: FilterChip(
                        label: Text(category),
                        selected: false,
                        onSelected: (selected) {
                          // Filter clubs by category
                        },
                      ),
                    );
                  }).toList(),
            ),
          ),
        ),

        // Clubs list
        Expanded(
          child: ListView.builder(
            itemCount: clubs.length,
            itemBuilder: (context, index) {
              final club = clubs[index];
              return Card(
                margin: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getCategoryColor(club['category']),
                    child: Text(club['name'][0]),
                  ),
                  title: Text(club['name']),
                  subtitle: Text(
                    '${club['category']} • ${club['members']} members',
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      AppRouter.clubDetailRoute,
                      arguments: club,
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMentorshipTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Find a Mentor',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text(
            'Complete your profile to get matched with the right mentor',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 24),

          // Mentorship profile form
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Your Mentorship Profile',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // Program dropdown
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Your Program',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        [
                              'Computer Science',
                              'Engineering',
                              'Business',
                              'Arts',
                              'Medicine',
                            ]
                            .map(
                              (program) => DropdownMenuItem(
                                value: program,
                                child: Text(program),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 16),

                  // Year dropdown
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Your Year',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        [
                              'First Year',
                              'Second Year',
                              'Third Year',
                              'Fourth Year',
                            ]
                            .map(
                              (year) => DropdownMenuItem(
                                value: year,
                                child: Text(year),
                              ),
                            )
                            .toList(),
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 16),

                  // Interests
                  const Text('Select your interests (up to 3):'),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        [
                          'Academic Success',
                          'Career Planning',
                          'Research',
                          'Internships',
                          'Graduate School',
                          'Work-Life Balance',
                          'Campus Involvement',
                          'Leadership',
                          'Networking',
                        ].map((interest) {
                          return FilterChip(
                            label: Text(interest),
                            selected: false,
                            onSelected: (selected) {},
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: 24),

                  // Find mentor button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(context, AppRouter.mentorshipRoute);
                      },
                      child: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Text('Find My Mentor'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Recommended mentors
          const Text(
            'Recommended Mentors',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Card(
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue.shade100,
                child: const Text('S'),
              ),
              title: const Text('Dr. Sarah Chen'),
              subtitle: const Text('Associate Professor, Computer Science'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pushNamed(
                  context,
                  AppRouter.mentorDetailRoute,
                  arguments: {
                    'name': 'Dr. Sarah Chen',
                    'title': 'Associate Professor, Computer Science',
                    'expertise': ['Programming', 'AI', 'Research Methods'],
                    'bio':
                        'Dr. Chen specializes in artificial intelligence and has mentored over 30 students in research projects.',
                    'availability': 'Tuesdays and Thursdays, 2-4 PM',
                    'rating': 4.8,
                    'reviews': 15,
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTraditionsTab() {
    final List<Map<String, String>> traditions = [
      {
        'title': 'Freshman Week',
        'description':
            'A week-long orientation for new students with activities, games, and campus tours.',
      },
      {
        'title': 'The Midnight Yell',
        'description':
            'Students gather at midnight before big games to practice cheers and build school spirit.',
      },
      {
        'title': 'Campus Slang',
        'description':
            '"The Quad" - central campus area, "All-nighter" - studying all night, "The Caf" - main cafeteria.',
      },
      {
        'title': 'Survival Tips',
        'description':
            'Always bring a water bottle, know the shuttle schedule, and find the hidden study spots in the library basement.',
      },
    ];

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: traditions.length,
      itemBuilder: (context, index) {
        final tradition = traditions[index];
        return Card(
          elevation: 3,
          margin: const EdgeInsets.only(bottom: 16.0),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      index % 2 == 0 ? Icons.emoji_events : Icons.lightbulb,
                      color: Colors.amber,
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      tradition['title']!,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  tradition['description']!,
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Tech':
        return Colors.blue;
      case 'Arts':
        return Colors.purple;
      case 'Sports':
        return Colors.green;
      case 'Academic':
        return Colors.orange;
      case 'Cultural':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildPeerCard(Map<String, dynamic> peer) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            AppRouter.peerConnectDetailRoute,
            arguments: peer,
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.blue.shade200,
                    child: Text(
                      peer['name'][0],
                      style: const TextStyle(fontSize: 24, color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          peer['name'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${peer['program']} • ${peer['year']}',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          peer['bio'],
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Interests
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    (peer['interests'] as List<dynamic>).map((interest) {
                      return Chip(
                        label: Text(
                          interest,
                          style: const TextStyle(fontSize: 12),
                        ),
                        backgroundColor: Colors.blue.shade50,
                        padding: EdgeInsets.zero,
                        labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                      );
                    }).toList(),
              ),
              const SizedBox(height: 16),
              // Connect button
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      setState(() {
                        if (peer['connectionStatus'] == 'Connect') {
                          peer['connectionStatus'] = 'Pending';
                        } else if (peer['connectionStatus'] == 'Pending') {
                          peer['connectionStatus'] = 'Connect';
                        }
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor:
                          peer['connectionStatus'] == 'Connect'
                              ? Colors.blue
                              : Colors.grey,
                    ),
                    child: Text(peer['connectionStatus']),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        AppRouter.peerConnectDetailRoute,
                        arguments: peer,
                      );
                    },
                    child: const Text('View Profile'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Peers'),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView(
                  shrinkWrap: true,
                  children:
                      _availableFilters.map((filter) {
                        final isSelected = _selectedFilters.contains(filter);
                        return CheckboxListTile(
                          title: Text(filter),
                          value: isSelected,
                          onChanged: (value) {
                            setState(() {
                              if (value == true) {
                                _selectedFilters.add(filter);
                              } else {
                                _selectedFilters.remove(filter);
                              }
                            });
                          },
                        );
                      }).toList(),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    this.setState(() {});
                    Navigator.pop(context);
                  },
                  child: const Text('Apply'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _onBottomNavTap(int index) {
    if (index == widget.currentIndex) return;

    try {
      switch (index) {
        case 0:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
          break;
        case 1:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const MapScreen(currentIndex: 1),
            ),
          );
          break;
        case 2:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const EventsScreen(currentIndex: 2),
            ),
          );
          break;
        case 3:
          // Already on peer connect screen
          break;
        case 4:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const UpskillScreen(currentIndex: 4),
            ),
          );
          break;
      }
    } catch (e) {
      print('Navigation error: $e');
      // Fallback to named route
      final routes = ['/home', '/map', '/events', '/connect', '/upskill'];
      if (index < routes.length) {
        Navigator.pushReplacementNamed(context, routes[index]);
      }
    }
  }

  void _navigateToPeerDetail(Map<String, dynamic> peer) {
    Navigator.pushNamed(
      context,
      AppRouter.peerConnectDetailRoute,
      arguments: peer,
    );
  }
}
