import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../routes/app_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    // Navigate to onboarding screen after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {  // Check if widget is still mounted
        Navigator.of(context).pushReplacementNamed(AppRouter.onboardingRoute);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(
        0xFFDDEFEE,
      ), // Using the specified background color
      body: Center(
        child: SvgPicture.asset(
          'assets/images/splashscreen-image.svg',
          width: 120,
          height: 120,
        ),
      ),
    );
  }
}
