import 'package:flutter/material.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<Map<String, dynamic>> _notifications = [
    {
      'title': 'Assignment Due',
      'message': 'Your Research Paper is due tomorrow at 11:59 PM',
      'time': '2 hours ago',
      'read': false,
      'type': 'academic',
    },
    {
      'title': 'Event Reminder',
      'message': 'Career Fair starts in 3 hours at the Student Center',
      'time': '5 hours ago',
      'read': false,
      'type': 'event',
    },
    {
      'title': 'New Course Material',
      'message': 'Professor <PERSON> uploaded new lecture notes for CS101',
      'time': 'Yesterday',
      'read': true,
      'type': 'academic',
    },
    {
      'title': 'Club Meeting',
      'message': 'Programming Club meeting today at 5 PM in Room 302',
      'time': 'Yesterday',
      'read': true,
      'type': 'social',
    },
    {
      'title': 'Grade Posted',
      'message': 'Your grade for Math Quiz 3 has been posted',
      'time': '2 days ago',
      'read': true,
      'type': 'academic',
    },
  ];

  Icon _getNotificationIcon(String type) {
    switch (type) {
      case 'academic':
        return const Icon(Icons.school, color: Colors.blue);
      case 'event':
        return const Icon(Icons.event, color: Colors.orange);
      case 'social':
        return const Icon(Icons.people, color: Colors.green);
      default:
        return const Icon(Icons.notifications, color: Colors.grey);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                for (var notification in _notifications) {
                  notification['read'] = true;
                }
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('All notifications marked as read')),
              );
            },
            child: const Text('Mark all as read'),
          ),
        ],
      ),
      body: _notifications.isEmpty
          ? const Center(
              child: Text('No notifications'),
            )
          : ListView.builder(
              itemCount: _notifications.length,
              itemBuilder: (context, index) {
                final notification = _notifications[index];
                return Dismissible(
                  key: Key(index.toString()),
                  background: Container(
                    color: Colors.red,
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(right: 20.0),
                    child: const Icon(
                      Icons.delete,
                      color: Colors.white,
                    ),
                  ),
                  direction: DismissDirection.endToStart,
                  onDismissed: (direction) {
                    setState(() {
                      _notifications.removeAt(index);
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Notification dismissed')),
                    );
                  },
                  child: Card(
                    color: notification['read'] ? null : Colors.blue.shade50,
                    child: ListTile(
                      leading: _getNotificationIcon(notification['type']),
                      title: Text(
                        notification['title'],
                        style: TextStyle(
                          fontWeight: notification['read'] ? FontWeight.normal : FontWeight.bold,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(notification['message']),
                          const SizedBox(height: 4),
                          Text(
                            notification['time'],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      isThreeLine: true,
                      onTap: () {
                        setState(() {
                          notification['read'] = true;
                        });
                        // Navigate to relevant screen based on notification type
                        _handleNotificationTap(notification);
                      },
                    ),
                  ),
                );
              },
            ),
    );
  }

  void _handleNotificationTap(Map<String, dynamic> notification) {
    // Here you would navigate to the relevant screen based on notification type
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening: ${notification['title']}')),
    );
  }
}
