import 'package:flutter/material.dart';

class DeadlinesScreen extends StatefulWidget {
  const DeadlinesScreen({super.key});

  @override
  State<DeadlinesScreen> createState() => _DeadlinesScreenState();
}

class _DeadlinesScreenState extends State<DeadlinesScreen> {
  final List<Map<String, dynamic>> _deadlines = [];
  final _formKey = GlobalKey<FormState>();

  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _courseCodeController = TextEditingController();
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 7));
  TimeOfDay _selectedTime = TimeOfDay.now();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _courseCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Academic Deadlines')),
      body:
          _deadlines.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.assignment_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'No deadlines yet',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add your academic deadlines to get reminders',
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _showAddDeadlineDialog,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Deadline'),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                itemCount: _deadlines.length,
                itemBuilder: (context, index) {
                  final deadline = _deadlines[index];
                  final dueDate = deadline['dueDate'] as DateTime;
                  final daysLeft = dueDate.difference(DateTime.now()).inDays;

                  return Dismissible(
                    key: Key(deadline['id'].toString()),
                    background: Container(
                      color: Colors.red,
                      alignment: Alignment.centerRight,
                      padding: const EdgeInsets.only(right: 16.0),
                      child: const Icon(Icons.delete, color: Colors.white),
                    ),
                    direction: DismissDirection.endToStart,
                    onDismissed: (direction) {
                      setState(() {
                        _deadlines.removeAt(index);
                      });
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('${deadline['title']} removed'),
                          action: SnackBarAction(
                            label: 'UNDO',
                            onPressed: () {
                              setState(() {
                                _deadlines.insert(index, deadline);
                              });
                            },
                          ),
                        ),
                      );
                    },
                    child: Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: _getDeadlineColor(daysLeft),
                          child: Text(
                            daysLeft.toString(),
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(deadline['title']),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${deadline['courseCode']} - ${deadline['description']}',
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Due: ${_formatDateTime(dueDate)}',
                              style: TextStyle(
                                color: _getDeadlineColor(daysLeft),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        isThreeLine: true,
                        trailing: IconButton(
                          icon: Icon(
                            deadline['reminder']
                                ? Icons.notifications_active
                                : Icons.notifications_off,
                            color:
                                deadline['reminder']
                                    ? Colors.blue
                                    : Colors.grey,
                          ),
                          onPressed: () => _toggleReminder(index),
                        ),
                        onTap: () => _showDeadlineDetails(deadline),
                      ),
                    ),
                  );
                },
              ),
      floatingActionButton:
          _deadlines.isNotEmpty
              ? FloatingActionButton(
                onPressed: _showAddDeadlineDialog,
                child: const Icon(Icons.add),
              )
              : null,
    );
  }

  void _showAddDeadlineDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add New Deadline'),
            content: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        hintText: 'e.g., Final Project',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a title';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _courseCodeController,
                      decoration: const InputDecoration(
                        labelText: 'Course Code',
                        hintText: 'e.g., CS101',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a course code';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'e.g., Submit via Canvas',
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('Due Date'),
                      subtitle: Text(_formatDate(_selectedDate)),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final pickedDate = await showDatePicker(
                          context: context,
                          initialDate: _selectedDate,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (pickedDate != null) {
                          setState(() {
                            _selectedDate = pickedDate;
                          });
                        }
                      },
                    ),
                    ListTile(
                      title: const Text('Due Time'),
                      subtitle: Text(_selectedTime.format(context)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () async {
                        final pickedTime = await showTimePicker(
                          context: context,
                          initialTime: _selectedTime,
                        );
                        if (pickedTime != null) {
                          setState(() {
                            _selectedTime = pickedTime;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _resetForm();
                },
                child: const Text('CANCEL'),
              ),
              ElevatedButton(onPressed: _addDeadline, child: const Text('ADD')),
            ],
          ),
    );
  }

  void _addDeadline() {
    if (_formKey.currentState!.validate()) {
      final dueDate = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );

      final newDeadline = {
        'id': DateTime.now().millisecondsSinceEpoch,
        'title': _titleController.text,
        'courseCode': _courseCodeController.text,
        'description': _descriptionController.text,
        'dueDate': dueDate,
        'reminder': true,
      };

      setState(() {
        _deadlines.add(newDeadline);
        // Sort deadlines by due date
        _deadlines.sort(
          (a, b) =>
              (a['dueDate'] as DateTime).compareTo(b['dueDate'] as DateTime),
        );
      });

      // Schedule notification for this deadline
      _scheduleDeadlineNotification(newDeadline);

      Navigator.pop(context);
      _resetForm();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Deadline added: ${newDeadline['title']}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _toggleReminder(int index) {
    setState(() {
      _deadlines[index]['reminder'] = !_deadlines[index]['reminder'];
    });

    final deadline = _deadlines[index];

    if (deadline['reminder']) {
      // Schedule notification (placeholder)
      _scheduleDeadlineNotification(deadline);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Reminder set for ${deadline['title']}'),
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      // Cancel notification (placeholder)
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Reminder cancelled for ${deadline['title']}'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _scheduleDeadlineNotification(Map<String, dynamic> deadline) {
    // Notification scheduling will be implemented later
    // NotificationManager().scheduleAcademicDeadlineReminder(
    //   title: deadline['title'],
    //   description: deadline['description'],
    //   deadline: deadline['dueDate'],
    //   courseCode: deadline['courseCode'],
    // );
  }

  void _showDeadlineDetails(Map<String, dynamic> deadline) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  deadline['title'],
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  deadline['courseCode'],
                  style: const TextStyle(fontSize: 18, color: Colors.blue),
                ),
                const SizedBox(height: 16),
                Text(
                  deadline['description'],
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    const Icon(Icons.calendar_today),
                    const SizedBox(width: 8),
                    Text(
                      'Due: ${_formatDateTime(deadline['dueDate'])}',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        // TODO: Edit deadline
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit'),
                    ),
                    OutlinedButton.icon(
                      onPressed: () {
                        // TODO: Mark as completed
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.check),
                      label: const Text('Mark Complete'),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  void _resetForm() {
    _titleController.clear();
    _descriptionController.clear();
    _courseCodeController.clear();
    _selectedDate = DateTime.now().add(const Duration(days: 7));
    _selectedTime = TimeOfDay.now();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Color _getDeadlineColor(int daysLeft) {
    if (daysLeft < 0) {
      return Colors.red;
    } else if (daysLeft < 2) {
      return Colors.orange;
    } else if (daysLeft < 7) {
      return Colors.amber;
    } else {
      return Colors.green;
    }
  }
}
