import 'package:flutter/material.dart';

class MentorDetailScreen extends StatelessWidget {
  final Map<String, dynamic> mentorData;

  const MentorDetailScreen({super.key, required this.mentorData});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(mentorData['name'] ?? 'Mentor Details'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: CircleAvatar(
                radius: 50,
                child: Text(
                  (mentorData['name'] as String? ?? 'M')[0],
                  style: const TextStyle(fontSize: 40),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: Text(
                mentorData['name'] ?? 'Mentor Name',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
            ),
            Center(
              child: Text(
                mentorData['title'] ?? 'Title',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'Expertise',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: (mentorData['expertise'] as List<dynamic>? ?? [])
                  .map((e) => Chip(label: Text(e.toString())))
                  .toList(),
            ),
            const SizedBox(height: 16),
            Text(
              'Bio',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(mentorData['bio'] ?? 'No bio available.'),
            const SizedBox(height: 16),
            Text(
              'Availability',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(mentorData['availability'] ?? 'Not specified'),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber[700]),
                const SizedBox(width: 8),
                Text(
                  '${mentorData['rating'] ?? '0.0'} (${mentorData['reviews'] ?? '0'} reviews)',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Request mentorship logic
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Mentorship request sent!')),
                  );
                },
                child: const Text('Request Mentorship'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
