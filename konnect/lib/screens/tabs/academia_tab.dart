import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeline_tile/timeline_tile.dart';
import '../../providers/home_providers.dart';
import '../../screens/timetable_screen.dart';
import '../../utils/skeleton_theme.dart';

class AcademiaTab extends ConsumerStatefulWidget {
  const AcademiaTab({super.key});

  @override
  ConsumerState<AcademiaTab> createState() => _AcademiaTabState();
}

class _AcademiaTabState extends ConsumerState<AcademiaTab> {
  int _currentCalendarPage = 0;
  bool _isRefreshing = false;
  
  @override
  void initState() {
    super.initState();
    // Initial fetch is handled by the provider
  }
  
  // Refresh classes data
  Future<void> _refreshClasses() async {
    setState(() {
      _isRefreshing = true;
    });
    
    // Increment the refresh counter to trigger a refresh in the provider
    ref.read(classesRefreshProvider.notifier).state++;
    
    // Wait a bit to ensure the refresh animation is visible
    await Future.delayed(const Duration(milliseconds: 800));
    
    setState(() {
      _isRefreshing = false;
    });
  }
  
  String _getGreeting() {
    final now = DateTime.now();
    final hour = now.hour;
    final weekday = now.weekday;
    
    // Check if it's weekend (6 = Saturday, 7 = Sunday)
    final isWeekend = weekday == 6 || weekday == 7;
    
    if (isWeekend) {
      return 'Have a lovely weekend';
    } else if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  // Check if a class is currently in session
  bool _isCurrentClass(String startTime, String endTime) {
    final now = TimeOfDay.now();
    final nowMinutes = now.hour * 60 + now.minute;
    
    final start = _parseTimeString(startTime);
    final end = _parseTimeString(endTime);
    
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;
    
    return nowMinutes >= startMinutes && nowMinutes <= endMinutes;
  }
  
  // Parse time string (e.g., "09:00") to TimeOfDay
  TimeOfDay _parseTimeString(String timeStr) {
    final parts = timeStr.split(':');
    if (parts.length != 2) return const TimeOfDay(hour: 0, minute: 0);
    
    return TimeOfDay(
      hour: int.tryParse(parts[0]) ?? 0,
      minute: int.tryParse(parts[1]) ?? 0,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Force refresh when the refresh counter changes
    ref.watch(classesRefreshProvider);
    
    final userData = ref.watch(userDataProvider);
    final todayClassesAsync = ref.watch(todayClassesProvider);
    final theme = Theme.of(context);
    
    // App theme colors
    const primaryColor = Color(0xFF2A9D8F);
    final secondaryColor = const Color(0xFF264653);
    final backgroundColor = Colors.grey[50];
    final cardColor = Colors.white;
    
    return Theme(
      data: Theme.of(context).copyWith(
        cardTheme: CardTheme(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey[200]!),
          ),
          color: cardColor,
        ),
        dividerTheme: DividerThemeData(
          color: Colors.grey[200],
          thickness: 1,
        ),
      ),
      child: SafeArea(
        top: false,
        bottom: false,
        child: Builder(
          builder: (BuildContext context) {
            return RefreshIndicator(
              onRefresh: _refreshClasses,
              child: CustomScrollView(
                slivers: <Widget>[
                  SliverOverlapInjector(
                    handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                  ),
                  
                  // Personalized Greeting with gradient background
                  SliverToBoxAdapter(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [primaryColor, primaryColor.withOpacity(0.7)],
                        ),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(24),
                          bottomRight: Radius.circular(24),
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(24.0, 20.0, 24.0, 20.0),
                      margin: const EdgeInsets.only(bottom: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${_getGreeting()}, ${userData['name']}!',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            'Ready for your classes today?',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white.withOpacity(0.9),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Today's Academic Schedule
                  _buildSectionHeader('Today\'s Schedule', Icons.schedule),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: todayClassesAsync.when(
                        data: (classes) => _buildClassCards(classes, primaryColor),
                        loading: () => SkeletonTheme.create(
                          child: Column(
                            children: List.generate(3, (index) {
                              return Container(
                                margin: const EdgeInsets.only(bottom: 8.0),
                                child: TimelineTile(
                                  alignment: TimelineAlign.manual,
                                  lineXY: 0.05,
                                  isFirst: index == 0,
                                  isLast: index == 2,
                                  indicatorStyle: IndicatorStyle(
                                    width: 12,
                                    height: 12,
                                    indicator: Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: primaryColor,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 2,
                                        ),
                                      ),
                                    ),
                                  ),
                                  beforeLineStyle: LineStyle(
                                    color: primaryColor.withOpacity(0.3),
                                    thickness: 2,
                                  ),
                                  afterLineStyle: LineStyle(
                                    color: primaryColor.withOpacity(0.3),
                                    thickness: 2,
                                  ),
                                  endChild: Card(
                                    margin: const EdgeInsets.only(left: 8.0, right: 0, bottom: 8.0),
                                    child: Padding(
                                      padding: const EdgeInsets.all(12.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Container(
                                                padding: const EdgeInsets.symmetric(
                                                  horizontal: 8.0, 
                                                  vertical: 4.0,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: primaryColor.withOpacity(0.1),
                                                  borderRadius: BorderRadius.circular(4.0),
                                                ),
                                                child: const Text(
                                                  "CS101",
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 12.0,
                                                  ),
                                                ),
                                              ),
                                              const Text(
                                                "09:00 - 11:00",
                                                style: TextStyle(
                                                  fontSize: 12.0,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8.0),
                                          const Text(
                                            "Introduction to Computer Science",
                                            style: TextStyle(
                                              fontSize: 14.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 8.0),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.location_on_outlined,
                                                size: 12.0,
                                                color: Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4.0),
                                              const Text(
                                                "Room 101",
                                                style: TextStyle(
                                                  fontSize: 12.0,
                                                ),
                                              ),
                                              const SizedBox(width: 12.0),
                                              Icon(
                                                Icons.person_outline,
                                                size: 12.0,
                                                color: Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4.0),
                                              const Text(
                                                "Dr. John Smith",
                                                style: TextStyle(
                                                  fontSize: 12.0,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }),
                          ),
                        ),
                        error: (error, stack) => _buildErrorWidget(error, primaryColor),
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20.0, 6.0, 20.0, 12.0),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: TextButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const TimetableScreen(
                                  showNavBar: true,
                                ),
                              ),
                            );
                          },
                          icon: const Icon(Icons.calendar_month_rounded, size: 16),
                          label: Text(
                            'View Full Schedule',
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                          style: TextButton.styleFrom(
                            foregroundColor: primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Academic Calendar Highlights (reduced spacing)
                  _buildSectionHeader('Academic Calendar', Icons.event_note),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20.0, 4.0, 20.0, 12.0),
                      child: _buildAcademicCalendar(primaryColor),
                    ),
                  ),

                  // Study Materials (reduced spacing)
                  _buildSectionHeader('Study Materials', Icons.book),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20.0, 4.0, 20.0, 12.0),
                      child: _buildStudyMaterialsGrid(primaryColor, secondaryColor),
                    ),
                  ),

                  // Academic Support (reduced spacing)
                  _buildSectionHeader('Academic Support', Icons.support),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20.0, 4.0, 20.0, 16.0),
                      child: _buildAcademicSupportSection(primaryColor, secondaryColor),
                    ),
                  ),

                  // Bottom padding
                  SliverToBoxAdapter(
                    child: SizedBox(height: 16),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  SliverToBoxAdapter _buildSectionHeader(String title, IconData icon) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 12.0, 16.0, 6.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF2A9D8F).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF2A9D8F), size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF264653),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClassCards(List<Map<String, dynamic>> classes, Color primaryColor) {
    final now = DateTime.now();
    final weekday = now.weekday;
    final isWeekend = weekday == 6 || weekday == 7;
    
    if (isWeekend) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.weekend, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                const Text(
                  'It\'s the weekend!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF757575),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Take some time to relax',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF9E9E9E),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    
    if (classes.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.event_available, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                const Text(
                  'No classes today!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF757575),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Enjoy your free time',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF9E9E9E),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    
    return Column(
      children: List.generate(classes.length, (index) {
        final classItem = classes[index];
        
        // Extract course code and name
        final courseCode = classItem['code'] ?? classItem['name'].split(':').first.trim();
        final courseName = classItem['name'] ?? '';
        
        // Get location and professor
        final location = classItem['location'] ?? 'Unknown Location';
        final professor = classItem['professor'] ?? 'Unknown Instructor';
        
        // Get start and end times
        final startTime = classItem['startTime'] ?? '00:00';
        final endTime = classItem['endTime'] ?? '00:00';
        final timeRange = '$startTime - $endTime';
        
        // Check if this is the current class
        final isCurrentClass = _isCurrentClass(startTime, endTime);
        
        return Container(
          margin: const EdgeInsets.only(bottom: 8.0),
          child: TimelineTile(
            alignment: TimelineAlign.manual,
            lineXY: 0.05, // Reduced from 0.1 to minimize left space
            isFirst: index == 0,
            isLast: index == classes.length - 1,
            indicatorStyle: IndicatorStyle(
              width: 12,
              height: 12,
              indicator: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isCurrentClass ? Colors.green : primaryColor,
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
                child: isCurrentClass 
                    ? const Center(
                        child: Icon(
                          Icons.play_arrow,
                          size: 8,
                          color: Colors.white,
                        ),
                      )
                    : null,
              ),
            ),
            beforeLineStyle: LineStyle(
              color: primaryColor.withOpacity(0.3),
              thickness: 2,
            ),
            afterLineStyle: LineStyle(
              color: primaryColor.withOpacity(0.3),
              thickness: 2,
            ),
            endChild: Card(
              margin: const EdgeInsets.only(left: 6),
              child: Column(
                children: [
                  // Top row: Course code and time
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 14.0, 16.0, 10.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getSubjectColor(courseCode.toString()).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            courseCode.toString(),
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                              color: _getSubjectColor(courseCode.toString()),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: isCurrentClass 
                                ? Colors.green.withOpacity(0.1) 
                                : primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.access_time_rounded,
                                size: 14,
                                color: isCurrentClass ? Colors.green : primaryColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                timeRange,
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 13,
                                  color: isCurrentClass ? Colors.green : primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const Divider(height: 1),
                  
                  // Middle row: Course details
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Subject Icon
                        Container(
                          width: 45,
                          height: 45,
                          decoration: BoxDecoration(
                            color: _getSubjectColor(courseCode.toString()).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Center(
                            child: Text(
                              courseCode.toString().substring(0, min(2, courseCode.toString().length)),
                              style: TextStyle(
                                color: _getSubjectColor(courseCode.toString()),
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                courseName.toString(),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                  height: 1.3,
                                ),
                              ),
                              const SizedBox(height: 3),
                              Row(
                                children: [
                                  Icon(
                                    Icons.person_outline_rounded,
                                    size: 14,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    professor,
                                    style: TextStyle(
                                      color: Colors.grey[700],
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const Divider(height: 1),
                  
                  // Bottom row: Location and actions
                  Padding(
                    padding: const EdgeInsets.all(14.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              size: 14,
                              color: Colors.grey[700],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              location,
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 13,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            _buildActionButton(Icons.map_outlined, 'Map', primaryColor),
                            const SizedBox(width: 8),
                            _buildActionButton(Icons.bookmark_border_rounded, 'Save', primaryColor),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildActionButton(IconData icon, String label, Color color) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor(String code) {
    if (code.startsWith('CS') || code.startsWith('cs')) {
      return Colors.blue[700]!;
    } else if (code.startsWith('MATH') || code.startsWith('math')) {
      return Colors.red[600]!;
    } else if (code.startsWith('ENG') || code.startsWith('eng')) {
      return Colors.purple[600]!;
    } else if (code.startsWith('PHYS') || code.startsWith('phys')) {
      return Colors.orange[600]!;
    } else if (code.startsWith('CHEM') || code.startsWith('chem')) {
      return Colors.green[600]!;
    } else if (code.startsWith('BIO') || code.startsWith('bio')) {
      return Colors.teal[600]!;
    } else if (code.startsWith('PSYC') || code.startsWith('psyc')) {
      return Colors.pink[400]!;
    } else {
      return Colors.grey[700]!;
    }
  }

  Widget _buildAcademicCalendar(Color primaryColor) {
    // If loading, show skeleton
    if (_isRefreshing) {
      return SkeletonTheme.create(
        child: Column(
          children: [
            SizedBox(
              height: 200,
              child: PageView.builder(
                itemCount: 1,
                itemBuilder: (context, pageIndex) {
                  return Row(
                    children: List.generate(2, (index) {
                      return Expanded(
                        child: Card(
                          margin: const EdgeInsets.all(8.0),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8.0, 
                                    vertical: 4.0,
                                  ),
                                  decoration: BoxDecoration(
                                    color: primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(4.0),
                                  ),
                                  child: const Text(
                                    "EXAM",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12.0,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 8.0),
                                const Text(
                                  "Midterm Exams",
                                  style: TextStyle(
                                    fontSize: 14.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8.0),
                                const Text(
                                  "October 15-20, 2023",
                                  style: TextStyle(
                                    fontSize: 12.0,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
                  );
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(3, (index) {
                return Container(
                  width: index == 0 ? 24 : 8,
                  height: 8,
                  margin: const EdgeInsets.symmetric(horizontal: 2.0),
                  decoration: BoxDecoration(
                    color: index == 0 ? primaryColor : Colors.grey[300],
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                );
              }),
            ),
          ],
        ),
      );
    }

    // Sample academic events
    final academicEvents = [
      {
        'name': 'Midterm Exams',
        'date': 'October 15-20, 2023',
        'type': 'exam',
        'isUrgent': true,
        'code': 'EXAM',
      },
      {
        'name': 'Course Registration Deadline',
        'date': 'December 1, 2023',
        'type': 'deadline',
        'isUrgent': false,
        'code': 'REG',
      },
      {
        'name': 'Spring Break',
        'date': 'March 10-17, 2024',
        'type': 'break',
        'isUrgent': false,
        'code': 'BREAK',
      },
      {
        'name': 'Final Exams',
        'date': 'December 10-17, 2023',
        'type': 'exam',
        'isUrgent': false,
        'code': 'EXAM',
      },
    ];

    // Calculate number of carousel pages (2 items per page)
    final pageCount = (academicEvents.length / 2).ceil();

    return Column(
      children: [
        SizedBox(
          height: 200, // Reduced from 220
          child: PageView.builder(
            itemCount: pageCount,
            onPageChanged: (index) {
              setState(() {
                _currentCalendarPage = index;
              });
            },
            itemBuilder: (context, pageIndex) {
              // Calculate start and end indices for this page
              final startIndex = pageIndex * 2;
              final endIndex = startIndex + 2 > academicEvents.length 
                  ? academicEvents.length 
                  : startIndex + 2;
              
              // Get events for this page
              final pageEvents = academicEvents.sublist(startIndex, endIndex);
              
              return Column(
                children: pageEvents.map((event) {
                  Color color;
                  IconData icon;
                  
                  switch (event['type']) {
                    case 'exam':
                      icon = Icons.note_alt_outlined;
                      color = Colors.red[600]!;
                      break;
                    case 'deadline':
                      icon = Icons.alarm_outlined;
                      color = Colors.orange[600]!;
                      break;
                    case 'break':
                      icon = Icons.beach_access_outlined;
                      color = Colors.green[600]!;
                      break;
                    default:
                      icon = Icons.event_outlined;
                      color = Colors.blue[600]!;
                  }
                  
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8.0),
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(14.0),
                        child: Row(
                          children: [
                            // Event Icon
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: color.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: Icon(icon, color: color, size: 20),
                              ),
                            ),
                            const SizedBox(width: 14),
                            
                            // Event details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        event['code'] as String,
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 11,
                                          color: color,
                                        ),
                                      ),
                                      if (event['isUrgent'] as bool)
                                        Container(
                                          margin: const EdgeInsets.only(left: 8),
                                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.red[50],
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            'Upcoming',
                                            style: TextStyle(
                                              color: Colors.red[700],
                                              fontSize: 9,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 3),
                                  Text(
                                    event['name'] as String,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 3),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_today_rounded,
                                        size: 12,
                                        color: Colors.grey[600],
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        event['date'] as String,
                                        style: TextStyle(
                                          color: Colors.grey[700],
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ),
        
        // Carousel indicator dots
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            pageCount,
            (index) => AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: index == _currentCalendarPage ? 20 : 8,
              height: 8,
              margin: const EdgeInsets.symmetric(horizontal: 3),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: index == _currentCalendarPage
                    ? primaryColor
                    : Colors.grey[300],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStudyMaterialsGrid(Color primaryColor, Color secondaryColor) {
    if (_isRefreshing) {
      return SkeletonTheme.create(
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 12.0,
          mainAxisSpacing: 12.0,
          childAspectRatio: 1.6,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: List.generate(4, (index) {
            return Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      index == 0 ? Icons.local_library_outlined :
                      index == 1 ? Icons.menu_book_outlined :
                      index == 2 ? Icons.calendar_today_outlined :
                      Icons.access_time_filled_outlined,
                      size: 24,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      index == 0 ? "Library" :
                      index == 1 ? "Study Materials" :
                      index == 2 ? "Academic Calendar" :
                      "Office Hours",
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),
      );
    }

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 12.0,
      mainAxisSpacing: 12.0,
      childAspectRatio: 1.6,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildResourceTile('Library', Icons.local_library_outlined, secondaryColor),
        _buildResourceTile('Study Materials', Icons.menu_book_outlined, primaryColor),
        _buildResourceTile('Academic Calendar', Icons.calendar_today_outlined, const Color(0xFFE9C46A)),
        _buildResourceTile('Office Hours', Icons.access_time_filled_outlined, const Color(0xFFE76F51)),
      ],
    );
  }

  Widget _buildResourceTile(String title, IconData icon, Color color) {
    return Card(
      child: InkWell(
        onTap: () {},
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 22),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAcademicSupportSection(Color primaryColor, Color secondaryColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.support_agent_rounded,
                  color: primaryColor,
                  size: 22,
                ),
                const SizedBox(width: 10),
                Text(
                  'Get Help! 📚',
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Need assistance with your studies? Check out these resources:',
              style: const TextStyle(
                fontSize: 13,
                color: Colors.grey,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSupportTile(Icons.school_outlined, 'Tutoring', secondaryColor),
                _buildSupportTile(Icons.groups_outlined, 'Study Groups', const Color(0xFFE76F51)),
                _buildSupportTile(Icons.chat_outlined, 'Peer Mentors', primaryColor),
              ],
            ),
            const SizedBox(height: 14),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'Book an Appointment',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupportTile(IconData icon, String title, Color color) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(height: 6),
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }

  int min(int a, int b) {
    return a < b ? a : b;
  }

  // Error widget for API failures
  Widget _buildErrorWidget(Object error, Color primaryColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
            const SizedBox(height: 16),
            const Text(
              'Unable to load classes',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF757575),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Pull down to refresh',
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF9E9E9E),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _refreshClasses,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
