import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DiscoverTab extends ConsumerStatefulWidget {
  const DiscoverTab({super.key});

  @override
  ConsumerState<DiscoverTab> createState() => _DiscoverTabState();
}

class _DiscoverTabState extends ConsumerState<DiscoverTab> {
  final List<Map<String, dynamic>> _discoverItems = [
    {
      'title': 'Campus Events',
      'description': 'Discover upcoming events happening around campus',
      'icon': Icons.event,
      'color': const Color(0xFF2A9D8F),
    },
    {
      'title': 'Student Clubs',
      'description': 'Explore student clubs and organizations',
      'icon': Icons.people,
      'color': const Color(0xFFE9C46A),
    },
    {
      'title': 'Campus Resources',
      'description': 'Find resources available to students',
      'icon': Icons.school,
      'color': const Color(0xFFE76F51),
    },
    {
      'title': 'Campus Map',
      'description': 'Navigate your way around campus',
      'icon': Icons.map,
      'color': const Color(0xFF264653),
    },
  ];

  final List<Map<String, dynamic>> _trendingTopics = [
    {
      'title': 'Exam Preparation',
      'participants': 245,
      'icon': Icons.book,
      'color': Colors.blue,
    },
    {
      'title': 'Campus Activities',
      'participants': 189,
      'icon': Icons.celebration,
      'color': Colors.orange,
    },
    {
      'title': 'Career Opportunities',
      'participants': 312,
      'icon': Icons.work,
      'color': Colors.green,
    },
  ];

  final List<Map<String, dynamic>> _campusNews = [
    {
      'title': 'New Library Hours Announced',
      'date': 'October 10, 2023',
      'image': 'assets/images/library.jpg',
      'source': 'Campus News',
    },
    {
      'title': 'Student Council Elections Next Week',
      'date': 'October 15, 2023',
      'image': 'assets/images/election.jpg',
      'source': 'Student Affairs',
    },
    {
      'title': 'Tech Symposium Registration Open',
      'date': 'October 20, 2023',
      'image': 'assets/images/tech.jpg',
      'source': 'Engineering Department',
    },
  ];

  @override
  Widget build(BuildContext context) {
    // App theme colors - matching AcademiaTab
    const primaryColor = Color(0xFF2A9D8F);
    final secondaryColor = const Color(0xFF264653);
    final backgroundColor = Colors.grey[50];
    final cardColor = Colors.white;
    
    return Theme(
      data: Theme.of(context).copyWith(
        cardTheme: CardTheme(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey[200]!),
          ),
          color: cardColor,
        ),
        dividerTheme: DividerThemeData(
          color: Colors.grey[200],
          thickness: 1,
        ),
      ),
      child: SafeArea(
        top: false,
        bottom: false,
        child: Builder(
          builder: (BuildContext context) {
            return CustomScrollView(
              slivers: <Widget>[
                SliverOverlapInjector(
                  handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                
                // Discover Categories
                _buildSectionHeader('Explore Campus', Icons.explore),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: _buildDiscoverGrid(primaryColor),
                  ),
                ),
                
                // Trending Topics
                _buildSectionHeader('Trending Topics', Icons.trending_up),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: _buildTrendingTopics(primaryColor),
                  ),
                ),
                
                // Campus News
                _buildSectionHeader('Campus News', Icons.newspaper),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20.0, 4.0, 20.0, 16.0), // Reduced from 8.0, 24.0
                    child: _buildCampusNews(primaryColor),
                  ),
                ),
                
                // Upcoming Events
                _buildSectionHeader('Upcoming Events', Icons.event_note),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20.0, 4.0, 20.0, 16.0), // Reduced from 8.0, 24.0
                    child: _buildUpcomingEvents(primaryColor),
                  ),
                ),
                
                // Campus Spotlight
                _buildSectionHeader('Campus Spotlight', Icons.star),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20.0, 4.0, 20.0, 20.0), // Reduced from 8.0, 32.0
                    child: _buildCampusSpotlight(primaryColor, secondaryColor),
                  ),
                ),
                
                // Bottom padding
                SliverToBoxAdapter(
                  child: SizedBox(height: 20), // Reduced from 32
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  SliverToBoxAdapter _buildSectionHeader(String title, IconData icon) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0), // Reduced from 24.0, 12.0
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF2A9D8F).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF2A9D8F), size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF264653),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoverGrid(Color primaryColor) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16.0,
        mainAxisSpacing: 16.0,
        childAspectRatio: 1.5,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _discoverItems.length,
      itemBuilder: (context, index) {
        final item = _discoverItems[index];
        return Card(
          child: InkWell(
            onTap: () {},
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: (item['color'] as Color).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      item['icon'] as IconData,
                      color: item['color'] as Color,
                      size: 26,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    item['title'] as String,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTrendingTopics(Color primaryColor) {
    return Column(
      children: _trendingTopics.map((topic) {
        return Card(
          margin: const EdgeInsets.only(bottom: 12), // Reduced from 16
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Topic Icon
                Container(
                  width: 45,
                  height: 45,
                  decoration: BoxDecoration(
                    color: (topic['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Icon(topic['icon'] as IconData, color: topic['color'] as Color, size: 24),
                  ),
                ),
                const SizedBox(width: 16),
                
                // Topic details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        topic['title'] as String,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 14,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${topic['participants']} students discussing',
                            style: TextStyle(
                              color: Colors.grey[700],
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Arrow
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: primaryColor,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCampusNews(Color primaryColor) {
    return Column(
      children: [
        SizedBox(
          height: 220,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _campusNews.length,
            itemBuilder: (context, index) {
              final news = _campusNews[index];
              return Container(
                width: 280,
                margin: const EdgeInsets.only(right: 16),
                child: Card(
                  child: InkWell(
                    onTap: () {},
                    borderRadius: BorderRadius.circular(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Placeholder for image
                        Container(
                          height: 120,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                          ),
                          child: Center(
                            child: Icon(
                              Icons.photo,
                              color: Colors.grey[500],
                              size: 40,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                news['title'] as String,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                                    decoration: BoxDecoration(
                                      color: primaryColor.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Text(
                                      news['source'] as String,
                                      style: TextStyle(
                                        color: primaryColor,
                                        fontSize: 11,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Icon(
                                    Icons.calendar_today_rounded,
                                    size: 12,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    news['date'] as String,
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        // Horizontal scroller indicator dots
        Padding(
          padding: const EdgeInsets.only(top: 10.0), // Reduced from 16.0
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              3,
              (index) => Container(
                width: index == 0 ? 20 : 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 3),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: index == 0 ? primaryColor : Colors.grey[300],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUpcomingEvents(Color primaryColor) {
    final events = [
      {
        'title': 'Career Fair',
        'date': 'Oct 25',
        'time': '10:00 AM - 4:00 PM',
        'location': 'Student Center',
        'color': Colors.blue[700]!,
        'code': 'CAREER',
      },
      {
        'title': 'Cultural Festival',
        'date': 'Nov 5',
        'time': '12:00 PM - 8:00 PM',
        'location': 'Campus Quad',
        'color': Colors.orange[600]!,
        'code': 'FEST',
      },
      {
        'title': 'Guest Lecture: AI Ethics',
        'date': 'Nov 10',
        'time': '2:00 PM - 4:00 PM',
        'location': 'Lecture Hall 3',
        'color': Colors.purple[600]!,
        'code': 'LECTURE',
      },
    ];

    return Column(
      children: events.map((event) {
        return Container(
          margin: const EdgeInsets.only(bottom: 10.0), // Reduced from 16.0
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Event Icon
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: (event['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        event['date'].toString().split(' ')[0],
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: event['color'] as Color,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // Event details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: (event['color'] as Color).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                event['code'] as String,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 10,
                                  color: event['color'] as Color,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          event['title'] as String,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time_rounded,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              event['time'] as String,
                              style: TextStyle(
                                color: Colors.grey[700],
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              event['location'] as String,
                              style: TextStyle(
                                color: Colors.grey[700],
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Bookmark button
                  IconButton(
                    icon: Icon(Icons.bookmark_border_rounded, color: primaryColor),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCampusSpotlight(Color primaryColor, Color secondaryColor) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.star_rounded,
                  color: const Color(0xFFE9C46A),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Student Achievement',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Placeholder for student image
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Icon(
                  Icons.person,
                  color: Colors.grey[400],
                  size: 64,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Jane Doe - Computer Science',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Won the National Coding Competition and will represent the university at the International Hackathon next month.',
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
                color: Color(0xFF555555),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 14),
              decoration: BoxDecoration(
                color: primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  'Read Full Story',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
