class PaginatedResponse<T> {
  final List<T> listings;
  final int totalCount;
  final int pageCount;
  final int currentPage;
  final int pageSize;

  PaginatedResponse({
    required this.listings,
    required this.totalCount,
    required this.pageCount,
    required this.currentPage,
    required this.pageSize,
  });

  factory PaginatedResponse.fromJson(
      Map<String, dynamic> json, T Function(Map<String, dynamic>) fromJsonT) {
    return PaginatedResponse(
      listings: (json['listings'] as List)
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList(),
      totalCount: json['totalCount'],
      pageCount: json['pageCount'],
      currentPage: json['currentPage'],
      pageSize: json['pageSize'],
    );
  }
}