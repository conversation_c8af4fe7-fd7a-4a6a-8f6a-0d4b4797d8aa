class Housing {
  final int id;
  final String name;
  final String address;
  final double rent;
  final int bedrooms;
  final double bathrooms;
  final int squareFeet;
  final List<String> amenities;
  final String description;
  final String availability;
  final String contactName;
  final String contactEmail;
  final String contactPhone;
  final List<String> images;
  final bool isFurnished;
  final DateTime? createdAt;

  Housing({
    required this.id,
    required this.name,
    required this.address,
    required this.rent,
    required this.bedrooms,
    required this.bathrooms,
    required this.squareFeet,
    required this.amenities,
    required this.description,
    required this.availability,
    required this.contactName,
    required this.contactEmail,
    required this.contactPhone,
    required this.images,
    required this.isFurnished,
    this.createdAt,
  });

  factory Housing.fromJson(Map<String, dynamic> json) {
    return Housing(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      rent: json['rent'].toDouble(),
      bedrooms: json['bedrooms'],
      bathrooms: json['bathrooms'].toDouble(),
      squareFeet: json['squareFeet'],
      amenities: List<String>.from(json['amenities']),
      description: json['description'],
      availability: json['availability'],
      contactName: json['contactName'],
      contactEmail: json['contactEmail'],
      contactPhone: json['contactPhone'],
      images: List<String>.from(json['images']),
      isFurnished: json['isFurnished'],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    );
  }
}