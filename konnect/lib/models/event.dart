class Event {
  final String? id; // Keep as String for now to avoid breaking changes
  final String name;
  final String description;
  final String location;
  final String date;
  final String startTime;
  final String endTime;
  final String category;
  final String organizer;
  final bool isReminded;
  final bool isFavorite;
  final int attendees;

  Event({
    this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.category,
    required this.organizer,
    this.isReminded = false,
    this.isFavorite = false,
    this.attendees = 0,
  });

  // Create a copy of this Event with the given fields replaced
  Event copyWith({
    String? id,
    String? name,
    String? description,
    String? location,
    String? date,
    String? startTime,
    String? endTime,
    String? category,
    String? organizer,
    bool? isReminded,
    bool? isFavorite,
    int? attendees,
  }) {
    return Event(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      category: category ?? this.category,
      organizer: organizer ?? this.organizer,
      isReminded: isReminded ?? this.isReminded,
      isFavorite: isFavorite ?? this.isFavorite,
      attendees: attendees ?? this.attendees,
    );
  }

  // Factory constructor to create an Event from JSON
  factory Event.fromJson(Map<String, dynamic> json) {
    return Event(
      id: json['id']?.toString(), // Convert to String to ensure consistency
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      date: json['date'] ?? '',
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      category: json['category'] ?? 'Other',
      organizer: json['organizer'] ?? '',
      isReminded: json['isReminded'] ?? false,
      isFavorite: json['isFavorite'] ?? false,
      attendees: json['attendees'] ?? 0,
    );
  }

  // Convert Event to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'date': date,
      'startTime': startTime,
      'endTime': endTime,
      'category': category,
      'organizer': organizer,
      'isReminded': isReminded,
      'isFavorite': isFavorite,
      'attendees': attendees,
    };
  }
}
