// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'dart:async';
// import 'dart:io';
// import 'package:timezone/timezone.dart' as tz;
// import 'package:timezone/data/latest.dart' as tz_data;

// class NotificationService {
//   static final NotificationService _instance = NotificationService._internal();
//   factory NotificationService() => _instance;
//   NotificationService._internal();

//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = 
//       FlutterLocalNotificationsPlugin();

//   static const String academicChannel = 'academic_channel';
//   static const String eventsChannel = 'events_channel';
//   static const String securityChannel = 'security_channel';
//   static const String socialChannel = 'social_channel';

//   Future<void> init() async {
//     // Initialize timezone
//     tz_data.initializeTimeZones();

//     // Initialize notification settings for Android
//     const AndroidInitializationSettings initializationSettingsAndroid =
//         AndroidInitializationSettings('@mipmap/ic_launcher');

//     // Initialize notification settings for iOS
//     final DarwinInitializationSettings initializationSettingsIOS =
//         DarwinInitializationSettings(
//       requestAlertPermission: true,
//       requestBadgePermission: true,
//       requestSoundPermission: true,
//       onDidReceiveLocalNotification: (int id, String? title, String? body, String? payload) async {
//         // Handle iOS foreground notification
//       },
//     );

//     // Combine platform-specific initialization settings
//     final InitializationSettings initializationSettings = InitializationSettings(
//       android: initializationSettingsAndroid,
//       iOS: initializationSettingsIOS,
//     );

//     // Initialize the plugin
//     await flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       onDidReceiveNotificationResponse: (NotificationResponse response) async {
//         // Handle notification tap
//         _handleNotificationTap(response.payload);
//       },
//     );

//     // Create notification channels for Android
//     if (Platform.isAndroid) {
//       await _createNotificationChannels();
//     }
//   }

//   Future<void> _createNotificationChannels() async {
//     // Academic channel
//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(
//           const AndroidNotificationChannel(
//             academicChannel,
//             'Academic Notifications',
//             description: 'Notifications about academic deadlines and classes',
//             importance: Importance.high,
//           ),
//         );

//     // Events channel
//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(
//           const AndroidNotificationChannel(
//             eventsChannel,
//             'Event Notifications',
//             description: 'Notifications about campus events',
//             importance: Importance.high,
//           ),
//         );

//     // Security channel
//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(
//           const AndroidNotificationChannel(
//             securityChannel,
//             'Security Alerts',
//             description: 'Important security alerts and notifications',
//             importance: Importance.max,
//             sound: RawResourceAndroidNotificationSound('alarm'),
//           ),
//         );

//     // Social channel
//     await flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(
//           const AndroidNotificationChannel(
//             socialChannel,
//             'Social Notifications',
//             description: 'Notifications about mentor responses and peer requests',
//             importance: Importance.high,
//           ),
//         );
//   }

//   Future<void> requestPermissions() async {
//     if (Platform.isIOS) {
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<
//               IOSFlutterLocalNotificationsPlugin>()
//           ?.requestPermissions(
//             alert: true,
//             badge: true,
//             sound: true,
//           );
//     } else if (Platform.isAndroid) {
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<
//               AndroidFlutterLocalNotificationsPlugin>()
//           ?.requestPermission();
//     }
//   }

//   Future<void> showAcademicNotification({
//     required int id,
//     required String title,
//     required String body,
//     String? payload,
//   }) async {
//     await flutterLocalNotificationsPlugin.show(
//       id,
//       title,
//       body,
//       NotificationDetails(
//         android: AndroidNotificationDetails(
//           academicChannel,
//           'Academic Notifications',
//           channelDescription: 'Notifications about academic deadlines and classes',
//           importance: Importance.high,
//           priority: Priority.high,
//           icon: '@mipmap/ic_launcher',
//         ),
//         iOS: const DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//         ),
//       ),
//       payload: payload,
//     );
//   }

//   Future<void> showEventNotification({
//     required int id,
//     required String title,
//     required String body,
//     String? payload,
//   }) async {
//     await flutterLocalNotificationsPlugin.show(
//       id,
//       title,
//       body,
//       NotificationDetails(
//         android: AndroidNotificationDetails(
//           eventsChannel,
//           'Event Notifications',
//           channelDescription: 'Notifications about campus events',
//           importance: Importance.high,
//           priority: Priority.high,
//           icon: '@mipmap/ic_launcher',
//         ),
//         iOS: const DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//         ),
//       ),
//       payload: payload,
//     );
//   }

//   Future<void> showSecurityAlert({
//     required int id,
//     required String title,
//     required String body,
//     String? payload,
//   }) async {
//     await flutterLocalNotificationsPlugin.show(
//       id,
//       title,
//       body,
//       NotificationDetails(
//         android: AndroidNotificationDetails(
//           securityChannel,
//           'Security Alerts',
//           channelDescription: 'Important security alerts and notifications',
//           importance: Importance.max,
//           priority: Priority.max,
//           icon: '@mipmap/ic_launcher',
//           fullScreenIntent: true,
//         ),
//         iOS: const DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//           interruptionLevel: InterruptionLevel.timeSensitive,
//         ),
//       ),
//       payload: payload,
//     );
//   }

//   Future<void> showSocialNotification({
//     required int id,
//     required String title,
//     required String body,
//     String? payload,
//   }) async {
//     await flutterLocalNotificationsPlugin.show(
//       id,
//       title,
//       body,
//       NotificationDetails(
//         android: AndroidNotificationDetails(
//           socialChannel,
//           'Social Notifications',
//           channelDescription: 'Notifications about mentor responses and peer requests',
//           importance: Importance.high,
//           priority: Priority.high,
//           icon: '@mipmap/ic_launcher',
//         ),
//         iOS: const DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//         ),
//       ),
//       payload: payload,
//     );
//   }

//   Future<void> scheduleAcademicNotification({
//     required int id,
//     required String title,
//     required String body,
//     required DateTime scheduledDate,
//     String? payload,
//   }) async {
//     await flutterLocalNotificationsPlugin.zonedSchedule(
//       id,
//       title,
//       body,
//       tz.TZDateTime.from(scheduledDate, tz.local),
//       NotificationDetails(
//         android: AndroidNotificationDetails(
//           academicChannel,
//           'Academic Notifications',
//           channelDescription: 'Notifications about academic deadlines and classes',
//           importance: Importance.high,
//           priority: Priority.high,
//           icon: '@mipmap/ic_launcher',
//         ),
//         iOS: const DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//         ),
//       ),
//       androidAllowWhileIdle: true,
//       uiLocalNotificationDateInterpretation:
//           UILocalNotificationDateInterpretation.absoluteTime,
//       payload: payload,
//     );
//   }

//   Future<void> scheduleEventNotification({
//     required int id,
//     required String title,
//     required String body,
//     required DateTime scheduledDate,
//     String? payload,
//   }) async {
//     await flutterLocalNotificationsPlugin.zonedSchedule(
//       id,
//       title,
//       body,
//       tz.TZDateTime.from(scheduledDate, tz.local),
//       NotificationDetails(
//         android: AndroidNotificationDetails(
//           eventsChannel,
//           'Event Notifications',
//           channelDescription: 'Notifications about campus events',
//           importance: Importance.high,
//           priority: Priority.high,
//           icon: '@mipmap/ic_launcher',
//         ),
//         iOS: const DarwinNotificationDetails(
//           presentAlert: true,
//           presentBadge: true,
//           presentSound: true,
//         ),
//       ),
//       androidAllowWhileIdle: true,
//       uiLocalNotificationDateInterpretation:
//           UILocalNotificationDateInterpretation.absoluteTime,
//       payload: payload,
//     );
//   }

//   Future<void> cancelNotification(int id) async {
//     await flutterLocalNotificationsPlugin.cancel(id);
//   }

//   Future<void> cancelAllNotifications() async {
//     await flutterLocalNotificationsPlugin.cancelAll();
//   }

//   void _handleNotificationTap(String? payload) {
//     // Handle notification tap based on payload
//     // This would typically navigate to the relevant screen
//     debugPrint('Notification tapped with payload: $payload');
    
//     // Example of how to handle different payloads:
//     if (payload != null) {
//       if (payload.startsWith('event:')) {
//         // Navigate to event details
//         final eventId = payload.split(':')[1];
//         // Navigator.pushNamed(context, '/event-detail', arguments: eventId);
//       } else if (payload.startsWith('academic:')) {
//         // Navigate to academic deadline details
//         final deadlineId = payload.split(':')[1];
//         // Navigator.pushNamed(context, '/academic-deadline', arguments: deadlineId);
//       } else if (payload.startsWith('mentor:')) {
//         // Navigate to mentor response
//         final mentorId = payload.split(':')[1];
//         // Navigator.pushNamed(context, '/mentor-detail', arguments: mentorId);
//       } else if (payload.startsWith('peer:')) {
//         // Navigate to peer request
//         final peerId = payload.split(':')[1];
//         // Navigator.pushNamed(context, '/peer-connect', arguments: peerId);
//       }
//     }
//   }
// }