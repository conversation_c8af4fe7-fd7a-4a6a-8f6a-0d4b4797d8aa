import 'dart:convert';
import 'dart:async'; // For TimeoutException
import 'dart:developer' as developer;
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
  
  @override
  String toString() => message;
}

class ApiClient {
  // Base URL for the API
  final String baseUrl = 'https://konnectbackend-jkkimunyi4805-yhf1iuq4.leapcell.dev';
  final http.Client _httpClient;
  final Connectivity _connectivity = Connectivity();

  ApiClient({http.Client? httpClient}) : _httpClient = httpClient ?? http.Client();

  Future<bool> _checkConnectivity() async {
    final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
    return results.isNotEmpty && 
           (results.contains(ConnectivityResult.wifi) || 
            results.contains(ConnectivityResult.mobile) || 
            results.contains(ConnectivityResult.ethernet));
  }

  Future<dynamic> get(String endpoint, {Duration? timeout}) async {
    try {
      developer.log('ApiClient: GET request to $endpoint');
      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(timeout ?? const Duration(seconds: 30));
      
      developer.log('ApiClient: Response status code: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        developer.log('ApiClient: Error response: ${response.body}');
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      developer.log('ApiClient: Exception during GET request: ${e.toString()}');
      if (e is TimeoutException) {
        throw Exception('Request timed out. Please check your internet connection.');
      }
      throw Exception('Network error: ${e.toString()}');
    }
  }

  Future<dynamic> post(String endpoint, Map<String, dynamic> data) async {
    if (!await _checkConnectivity()) {
      throw NetworkException('No internet connection');
    }
    
    try {
      final response = await _httpClient.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      ).timeout(const Duration(seconds: 10));

      return _processResponse(response);
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  Future<dynamic> put(String endpoint, Map<String, dynamic> data) async {
    if (!await _checkConnectivity()) {
      throw NetworkException('No internet connection');
    }
    
    try {
      final response = await _httpClient.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      ).timeout(const Duration(seconds: 10));

      return _processResponse(response);
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  Future<dynamic> delete(String endpoint) async {
    if (!await _checkConnectivity()) {
      throw NetworkException('No internet connection');
    }
    
    try {
      final response = await _httpClient.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      return _processResponse(response);
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  dynamic _processResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) return null;
      return json.decode(response.body);
    } else {
      throw NetworkException('API Error: ${response.statusCode} - ${response.body}');
    }
  }
}
