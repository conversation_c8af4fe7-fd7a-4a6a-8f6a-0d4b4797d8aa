import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:konnect/models/event.dart';
import 'package:konnect/services/api_client.dart';
import 'dart:developer' as developer;

// Parse events in background isolate
List<Event> _parseEvents(String responseBody) {
  final data = jsonDecode(responseBody) as List;
  return data.map((item) => Event.fromJson(item)).toList();
}

// Parse single event in background isolate
Event _parseEvent(String responseBody) {
  final data = jsonDecode(responseBody);
  return Event.fromJson(data);
}

class EventService {
  final ApiClient _apiClient;
  final Map<String, List<Event>> _eventCache = {};
  final Duration _cacheDuration = const Duration(minutes: 5);
  final Map<String, DateTime> _cacheTimestamps = {};
  
  EventService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();
  
  Future<List<Event>> getAllEvents() async {
    try {
      developer.log('EventService: Fetching all events');
      final data = await _apiClient.get('/api/events');
      
      // Use compute to parse JSON in background isolate
      return compute(_parseEvents, jsonEncode(data));
    } catch (e) {
      developer.log('EventService: Error fetching all events: ${e.toString()}');
      throw Exception('Failed to load events: ${e.toString()}');
    }
  }
  
  Future<List<Event>> getEventsByDate(DateTime date) async {
    try {
      // Check cache first
      final cacheKey = 'date_${date.toIso8601String()}';
      if (_eventCache.containsKey(cacheKey) && 
          DateTime.now().difference(_cacheTimestamps[cacheKey]!) < _cacheDuration) {
        developer.log('EventService: Returning cached events for date: $date');
        return _eventCache[cacheKey]!;
      }
      
      developer.log('EventService: Fetching events for date: $date');
      final data = await _apiClient.get('/api/events/date/${date.toIso8601String()}',
          timeout: const Duration(seconds: 10));
      
      // Use compute to parse JSON in background isolate
      final events = await compute(_parseEvents, jsonEncode(data));
      
      // Update cache
      _eventCache[cacheKey] = events;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      return events;
    } catch (e) {
      developer.log('EventService: Error fetching events by date: ${e.toString()}');
      throw Exception('Failed to load events: ${e.toString()}');
    }
  }
  
  Future<List<Event>> getEventsByCategory(String category) async {
    try {
      // Check cache first
      final cacheKey = 'category_$category';
      if (_eventCache.containsKey(cacheKey) && 
          DateTime.now().difference(_cacheTimestamps[cacheKey]!) < _cacheDuration) {
        developer.log('EventService: Returning cached events for category: $category');
        return _eventCache[cacheKey]!;
      }
      
      developer.log('EventService: Fetching events for category: $category');
      final data = await _apiClient.get('/api/events/category/$category',
          timeout: const Duration(seconds: 10));
      
      // Use compute to parse JSON in background isolate
      final events = await compute(_parseEvents, jsonEncode(data));
      
      // Update cache
      _eventCache[cacheKey] = events;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      return events;
    } catch (e) {
      developer.log('EventService: Error fetching events by category: ${e.toString()}');
      throw Exception('Failed to load events: ${e.toString()}');
    }
  }
  
  Future<Event> addEvent(Event event) async {
    try {
      developer.log('EventService: Adding new event');
      final data = await _apiClient.post('/api/events', event.toJson());
      
      // Process single event response in background
      return compute(_parseEvent, jsonEncode(data));
    } catch (e) {
      developer.log('EventService: Error adding event: ${e.toString()}');
      throw Exception('Failed to add event: ${e.toString()}');
    }
  }
  
  Future<Event> updateEvent(int id, Event event) async {
    try {
      developer.log('EventService: Updating event with id: $id');
      final data = await _apiClient.put('/api/events/$id', event.toJson());
      
      // Process single event response in background
      return compute(_parseEvent, jsonEncode(data));
    } catch (e) {
      developer.log('EventService: Error updating event: ${e.toString()}');
      throw Exception('Failed to update event: ${e.toString()}');
    }
  }
}
