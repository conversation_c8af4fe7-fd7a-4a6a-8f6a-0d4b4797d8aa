import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

class TimetableService {
  // Base URL for the API - using the Leapcell instance
  static const String baseUrl = 'https://konnectbackend-jkkimunyi4805-yhf1iuq4.leapcell.dev';

  // Get all classes
  static Future<List<Map<String, dynamic>>> getAllClasses() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/api/timetable'));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Failed to load classes: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Get classes for a specific date
  static Future<List<Map<String, dynamic>>> getClassesByDate(DateTime date) async {
    try {
      // Ensure consistent date formatting across the app
      final formattedDate = DateFormat('yyyy-MM-dd').format(date);
      final response = await http.get(Uri.parse('$baseUrl/api/timetable/$formattedDate'));
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        throw Exception('Failed to load classes: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Add a class
  static Future<Map<String, dynamic>> addClass(Map<String, dynamic> classData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/timetable'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(classData),
      );
      
      if (response.statusCode == 201) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to add class: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Update a class
  static Future<Map<String, dynamic>> updateClass(int id, Map<String, dynamic> classData) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/timetable/$id'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(classData),
      );
      
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to update class: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Delete a class
  static Future<void> deleteClass(int id) async {
    try {
      final response = await http.delete(Uri.parse('$baseUrl/api/timetable/$id'));
      
      if (response.statusCode != 204) {
        throw Exception('Failed to delete class: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}
