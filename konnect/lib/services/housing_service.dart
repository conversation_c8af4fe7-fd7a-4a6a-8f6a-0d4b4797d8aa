import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:konnect/models/housing.dart';
import 'package:konnect/models/paginated_response.dart';
import 'package:konnect/services/api_client.dart';

class HousingService {
  final ApiClient _apiClient;

  HousingService({ApiClient? apiClient}) : _apiClient = apiClient ?? ApiClient();

  // Parse housing listings in a separate isolate
  static List<Housing> _parseHousingListings(String responseBody) {
    developer.log('HousingService: Parsing response: $responseBody');
    try {
      final parsed = jsonDecode(responseBody) as List;
      return parsed.map<Housing>((json) => Housing.fromJson(json)).toList();
    } catch (e) {
      developer.log('HousingService: Error parsing housing listings: $e');
      developer.log('HousingService: Response type: ${responseBody.runtimeType}');
      rethrow;
    }
  }

  // Parse paginated housing response in a separate isolate
  static PaginatedResponse<Housing> _parsePaginatedHousing(String responseBody) {
    final parsed = jsonDecode(responseBody) as Map<String, dynamic>;
    return PaginatedResponse.fromJson(parsed, Housing.fromJson);
  }

  // Get all housing listings with option to force refresh
  Future<List<Housing>> getAllHousing({bool forceRefresh = false}) async {
    try {
      developer.log('HousingService: Fetching all housing listings${forceRefresh ? " (forced refresh)" : ""}');
      
      // Add cache-busting parameter if forcing refresh
      final endpoint = forceRefresh 
          ? '/api/housing?_t=${DateTime.now().millisecondsSinceEpoch}' 
          : '/api/housing';
          
      final response = await _apiClient.get(endpoint);
      
      developer.log('HousingService: Got response: ${response.runtimeType}');
      
      // Check if response is already a List or needs to be encoded
      if (response is List) {
        developer.log('HousingService: Response is already a List with ${response.length} items');
        return compute(_parseHousingListings, jsonEncode(response));
      } else {
        developer.log('HousingService: Response is not a List: $response');
        throw Exception('Unexpected response format');
      }
    } catch (e) {
      developer.log('HousingService: Error in getAllHousing: $e');
      throw Exception('Failed to load housing listings: $e');
    }
  }

  // Get paginated housing listings
  Future<PaginatedResponse<Housing>> getPaginatedHousing(int page, int pageSize) async {
    try {
      final response = await _apiClient.get(
        '/api/housing/paginated?page=$page&pageSize=$pageSize',
      );
      return compute(_parsePaginatedHousing, jsonEncode(response));
    } catch (e) {
      throw Exception('Failed to load paginated housing listings: $e');
    }
  }

  // Get housing by ID
  Future<Housing> getHousingById(int id) async {
    try {
      final response = await _apiClient.get('/api/housing/$id');
      return Housing.fromJson(response);
    } catch (e) {
      throw Exception('Failed to load housing details: $e');
    }
  }

  // Search housing with filters
  Future<List<Housing>> searchHousing({
    double? minRent,
    double? maxRent,
    int? minBeds,
    int? maxBeds,
    bool? furnished,
  }) async {
    try {
      // Build query parameters
      final queryParams = <String, String>{};
      if (minRent != null && minRent > 0) queryParams['minRent'] = minRent.toString();
      if (maxRent != null && maxRent > 0) queryParams['maxRent'] = maxRent.toString();
      if (minBeds != null && minBeds > 0) queryParams['minBeds'] = minBeds.toString();
      if (maxBeds != null && maxBeds > 0) queryParams['maxBeds'] = maxBeds.toString();
      if (furnished != null) queryParams['furnished'] = furnished.toString();

      // Build query string
      String queryString = '';
      if (queryParams.isNotEmpty) {
        queryString = '?${queryParams.entries.map((e) => '${e.key}=${e.value}').join('&')}';
      }

      final response = await _apiClient.get('/api/housing/search$queryString');
      return compute(_parseHousingListings, jsonEncode(response));
    } catch (e) {
      throw Exception('Failed to search housing listings: $e');
    }
  }
}
