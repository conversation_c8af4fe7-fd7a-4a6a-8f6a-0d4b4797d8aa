import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiDebug {
  // Base URL for the API
  static const String baseUrl = 'https://konnectbackend-jkkimunyi4805-yhf1iuq4.leapcell.dev';
  
  // Method to test an endpoint and return the response
  static Future<String> testEndpoint(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Format JSON for better readability
        try {
          final jsonData = json.decode(response.body);
          return json.encode(jsonData);
        } catch (e) {
          // If not valid JSON, return as is
          return response.body;
        }
      } else {
        return 'Error: ${response.statusCode} - ${response.body}';
      }
    } catch (e) {
      return 'Exception: ${e.toString()}';
    }
  }
}
