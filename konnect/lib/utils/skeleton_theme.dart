import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// Provides consistent skeleton theming across the app
class SkeletonTheme {
  /// Primary skeleton color - light teal that complements the app's primary color
  static const Color baseColor = Color(0xFFE0F2F1);
  
  /// Highlight color for the shimmer effect
  static const Color highlightColor = Colors.white;
  
  /// Creates a consistently themed Skeletonizer widget
  static Widget create({
    required Widget child,
    bool enabled = true,
  }) {
    return Skeletonizer(
      enabled: enabled,
      containersColor: baseColor,
      effect: ShimmerEffect(
        baseColor: baseColor,
        highlightColor: highlightColor,
      ),
      child: child,
    );
  }
  
  /// Creates a dark mode themed Skeletonizer widget
  static Widget createDark({
    required Widget child,
    bool enabled = true,
  }) {
    return Skeletonizer(
      enabled: enabled,
      containersColor: const Color(0xFF1D3B38), // Darker shade for dark mode
      effect: ShimmerEffect(
        baseColor: const Color(0xFF1D3B38),
        highlightColor: const Color(0xFF2A4D49),
      ),
      child: child,
    );
  }
  
  /// Creates a Skeletonizer widget that automatically adapts to the current theme
  static Widget adaptive({
    required BuildContext context,
    required Widget child,
    bool enabled = true,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return isDarkMode 
        ? createDark(child: child, enabled: enabled)
        : create(child: child, enabled: enabled);
  }
}
