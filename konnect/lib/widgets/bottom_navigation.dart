import 'package:flutter/material.dart';

class BottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const BottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure currentIndex is within valid range
    final validIndex = _getValidIndex(currentIndex);
    
    return BottomNavigationBar(
      currentIndex: validIndex,
      onTap: onTap,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF2A9D8F),
      unselectedItemColor: Colors.grey,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(icon: Icon(Icons.map), label: 'Map'),
        BottomNavigationBarItem(icon: Icon(Icons.event), label: 'Events'),
        BottomNavigationBarItem(icon: Icon(Icons.people), label: 'Connect'),
        BottomNavigationBarItem(icon: Icon(Icons.school), label: 'Upskill'),
      ],
    );
  }
  
  // Helper method to ensure index is valid
  int _getValidIndex(int index) {
    const int itemCount = 5; // Number of items in the bottom navigation bar
    
    if (index < 0) {
      return 0; // Default to first tab if negative
    } else if (index >= itemCount) {
      return itemCount - 1; // Default to last tab if too large
    }
    
    return index;
  }
}
