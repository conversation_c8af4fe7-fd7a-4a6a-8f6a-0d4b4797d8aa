import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:konnect/routes/app_router.dart';
import 'package:konnect/widgets/connectivity_wrapper.dart';

void main() {
  // Ensure Flutter is initialized within the same zone as runApp
  runZonedGuarded(
    () {
      // Initialize Flutter binding inside the zone
      WidgetsFlutterBinding.ensureInitialized();

      // Initialize date formatting
      Intl.defaultLocale = 'en_US';

      // Set up error handling
      FlutterError.onError = (FlutterErrorDetails details) {
        FlutterError.presentError(details);
        print('Flutter error caught: ${details.exception}');
        print('Stack trace: ${details.stack}');
      };

      // Run the app in the same zone
      runApp(const ProviderScope(child: MainApp()));
    },
    (error, stackTrace) {
      print('Caught error in runZonedGuarded: $error');
      print('Stack trace: $stackTrace');
    },
  );
}

class MainApp extends ConsumerWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'Konnect',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
        primaryColor: const Color(0xFF2A9D8F),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2A9D8F),
          primary: const Color(0xFF2A9D8F),
        ),
      ),
      darkTheme: ThemeData(
        brightness: Brightness.dark,
        useMaterial3: true,
        primaryColor: const Color(0xFF2A9D8F),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2A9D8F),
          primary: const Color(0xFF2A9D8F),
          brightness: Brightness.dark,
        ),
      ),
      themeMode:
          ThemeMode
              .light, // Always use light theme regardless of system settings
      builder: (context, child) {
        return ConnectivityWrapper(child: child!);
      },
      initialRoute: AppRouter.splashRoute,
      routes: AppRouter.routes,
      onGenerateRoute: AppRouter.onGenerateRoute,
      debugShowCheckedModeBanner: false,
    );
  }
}
