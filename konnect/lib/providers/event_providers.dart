import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:konnect/models/event.dart';
import 'package:konnect/services/event_service.dart';
import 'package:intl/intl.dart';

// Service provider
final eventServiceProvider = Provider<EventService>((ref) {
  return EventService();
});

// Selected date provider
final selectedDateProvider = StateProvider<DateTime>((ref) => DateTime.now());

// Loading state provider
final eventsLoadingProvider = StateProvider<bool>((ref) => false);

// Error state provider
final eventsErrorProvider = StateProvider<String?>((ref) => null);

// Events provider
final eventsProvider = FutureProvider.family<List<Event>, String>((ref, date) async {
  ref.read(eventsLoadingProvider.notifier).state = true;
  
  try {
    final eventService = ref.watch(eventServiceProvider);
    final events = await eventService.getEventsByDate(date as DateTime);
    ref.read(eventsErrorProvider.notifier).state = null;
    return events;
  } catch (e) {
    ref.read(eventsErrorProvider.notifier).state = e.toString();
    return [];
  } finally {
    ref.read(eventsLoadingProvider.notifier).state = false;
  }
});

// Filtered events provider
final filteredEventsProvider = Provider<List<Event>>((ref) {
  final date = ref.watch(selectedDateProvider);
  final formattedDate = DateFormat('yyyy-MM-dd').format(date);
  final eventsAsync = ref.watch(eventsProvider(formattedDate));
  
  return eventsAsync.when(
    data: (events) => events,
    loading: () => [],
    error: (_, __) => [],
  );
});

// Event count provider
final eventCountProvider = Provider<int>((ref) {
  return ref.watch(filteredEventsProvider).length;
});


