import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/timetable_service.dart';

// Current tab index provider
final currentTabProvider = StateProvider<int>((ref) => 0);

// User data provider
final userDataProvider = Provider<Map<String, dynamic>>((ref) {
  return {
    'name': '<PERSON>',
    'program': 'Computer Science',
    'year': 'Second Year',
  };
});

// Classes refresh provider to force refresh
final classesRefreshProvider = StateProvider<int>((ref) => 0);

// Today's classes provider that fetches real data
final todayClassesProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  // Watch the refresh counter to trigger refreshes
  ref.watch(classesRefreshProvider);
  
  final today = DateTime.now();
  final weekday = today.weekday;
  
  // Check if it's weekend (6 = Saturday, 7 = Sunday)
  final isWeekend = weekday == 6 || weekday == 7;
  
  // Return empty list on weekends without making API calls
  if (isWeekend) {
    return [];
  }
  
  try {
    // Use the exact same method as TimetableScreen
    final classes = await TimetableService.getClassesByDate(today);
    return classes;
  } catch (e) {
    // If there's an error, rethrow it so we can handle it in the UI
    throw Exception('Failed to load today\'s classes: $e');
  }
});

// Assignments provider
final assignmentsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return [
    {
      'title': 'Research Paper',
      'course': 'English Composition',
      'dueDate': 'Tomorrow',
      'progressValue': 0.75,
      'progress': '75% Complete',
    },
    {
      'title': 'Problem Set 3',
      'course': 'Calculus II',
      'dueDate': 'Oct 15',
      'progressValue': 0.5,
      'progress': '50% Complete',
    },
    {
      'title': 'Lab Report',
      'course': 'Physics 101',
      'dueDate': 'Oct 20',
      'progressValue': 0.25,
      'progress': '25% Complete',
    },
  ];
});

// Events provider
final eventsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return [
    {
      'title': 'Tech Meetup',
      'time': 'Tomorrow, 6:00 PM',
      'location': 'Student Center',
      'icon': 'computer',
      'color': '0xFF264653',
    },
    {
      'title': 'Career Fair',
      'time': 'Friday, 10:00 AM',
      'location': 'Main Hall',
      'icon': 'work',
      'color': '0xFFE76F51',
    },
    {
      'title': 'Study Group',
      'time': 'Today, 4:00 PM',
      'location': 'Library, Room 3',
      'icon': 'group',
      'color': '0xFF2A9D8F',
    },
  ];
});

// Clubs provider
final clubsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  return [
    {
      'name': 'Tech Club',
      'members': '42 members',
      'icon': 'computer',
      'color': '0xFF264653',
    },
    {
      'name': 'Art Society',
      'members': '38 members',
      'icon': 'palette',
      'color': '0xFFE76F51',
    },
    {
      'name': 'Sports Team',
      'members': '56 members',
      'icon': 'sports_soccer',
      'color': '0xFF2A9D8F',
    },
  ];
});

// News provider
final campusNewsProvider = Provider<List<Map<String, String>>>((ref) {
  return [
    {
      'title': 'New Library Hours Announced',
      'description': 'The university library will now be open until midnight on weekdays.',
      'timeAgo': '2 hours ago',
    },
    {
      'title': 'Campus Wi-Fi Upgrade',
      'description': 'Faster internet is coming to all campus buildings next week.',
      'timeAgo': '1 day ago',
    },
  ];
});

// Student deals provider
final studentDealsProvider = Provider<List<Map<String, String>>>((ref) {
  return [
    {
      'place': 'Campus Cafe',
      'offer': '20% off with student ID',
      'validity': 'Valid until end of semester',
    },
    {
      'place': 'University Bookstore',
      'offer': '15% discount on all textbooks',
      'validity': 'This week only',
    },
  ];
});
