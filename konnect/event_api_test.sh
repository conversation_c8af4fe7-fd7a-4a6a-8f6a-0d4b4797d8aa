#!/bin/bash
# Comprehensive test script for events API

# Base URL for the API
BASE_URL="ec2-3-145-32-152.us-east-2.compute.amazonaws.com:8080/api/events"

# Test 1: Get all events
echo "Testing GET all events..."
curl -s -X GET "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" | jq
echo -e "\n\n"

# Test 2: Add Academic events
echo "Adding Academic events..."
curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Freshman Orientation",
    "description": "Welcome event for all new students.",
    "location": "Main Auditorium",
    "date": "2023-09-15",
    "startTime": "09:00",
    "endTime": "12:00",
    "category": "Academic",
    "organizer": "Student Affairs",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 120
  }' | jq
echo -e "\n"

curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Career Fair",
    "description": "Annual career fair with over 50 companies.",
    "location": "Student Center",
    "date": "2023-10-20",
    "startTime": "10:00",
    "endTime": "16:00",
    "category": "Academic",
    "organizer": "Career Services",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 500
  }' | jq
echo -e "\n\n"

# Test 3: Add Social events
echo "Adding Social events..."
curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Campus Movie Night",
    "description": "Outdoor movie screening on the quad.",
    "location": "Main Quad",
    "date": "2023-09-22",
    "startTime": "19:00",
    "endTime": "22:00",
    "category": "Social",
    "organizer": "Student Activities Board",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 200
  }' | jq
echo -e "\n"

curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "International Food Festival",
    "description": "Celebrate cultural diversity through food.",
    "location": "Student Center Plaza",
    "date": "2023-10-05",
    "startTime": "11:00",
    "endTime": "15:00",
    "category": "Social",
    "organizer": "International Student Association",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 350
  }' | jq
echo -e "\n\n"

# Test 4: Add Sports events
echo "Adding Sports events..."
curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Homecoming Football Game",
    "description": "Annual homecoming game vs. rival university.",
    "location": "University Stadium",
    "date": "2023-10-14",
    "startTime": "13:00",
    "endTime": "16:00",
    "category": "Sports",
    "organizer": "Athletics Department",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 15000
  }' | jq
echo -e "\n"

curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Intramural Basketball Tournament",
    "description": "Campus-wide basketball tournament.",
    "location": "Recreation Center",
    "date": "2023-11-10",
    "startTime": "09:00",
    "endTime": "18:00",
    "category": "Sports",
    "organizer": "Campus Recreation",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 120
  }' | jq
echo -e "\n\n"

# Test 5: Add Club events
echo "Adding Club events..."
curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Coding Hackathon",
    "description": "24-hour coding competition with prizes.",
    "location": "Computer Science Building",
    "date": "2023-10-28",
    "startTime": "12:00",
    "endTime": "12:00",
    "category": "Club",
    "organizer": "Computer Science Club",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 75
  }' | jq
echo -e "\n"

curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Environmental Cleanup",
    "description": "Campus and community cleanup event.",
    "location": "Meet at Student Center",
    "date": "2023-09-30",
    "startTime": "09:00",
    "endTime": "12:00",
    "category": "Club",
    "organizer": "Environmental Club",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 50
  }' | jq
echo -e "\n\n"

# Test 6: Add Workshop events
echo "Adding Workshop events..."
curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Resume Building Workshop",
    "description": "Learn how to create an effective resume.",
    "location": "Career Center, Room 102",
    "date": "2023-09-18",
    "startTime": "14:00",
    "endTime": "16:00",
    "category": "Workshop",
    "organizer": "Career Services",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 30
  }' | jq
echo -e "\n"

curl -s -X POST "http://$BASE_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Financial Literacy Seminar",
    "description": "Learn about budgeting, saving, and investing.",
    "location": "Business Building, Room 305",
    "date": "2023-10-12",
    "startTime": "15:00",
    "endTime": "17:00",
    "category": "Workshop",
    "organizer": "Finance Club",
    "isReminded": false,
    "isFavorite": false,
    "attendees": 45
  }' | jq
echo -e "\n\n"

# Test 7: Get events by category
echo "Testing GET events by category..."
echo "Academic events:"
curl -s -X GET "http://$BASE_URL/category/Academic" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" | jq
echo -e "\n\n"

echo "Social events:"
curl -s -X GET "http://$BASE_URL/category/Social" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" | jq
echo -e "\n\n"

# Test 8: Get events by date
echo "Testing GET events by date..."
echo "Events on 2023-09-15:"
curl -s -X GET "http://$BASE_URL/date/2023-09-15" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" | jq
echo -e "\n\n"

echo "Events on 2023-10-20:"
curl -s -X GET "http://$BASE_URL/date/2023-10-20" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" | jq
echo -e "\n\n"

# Test 9