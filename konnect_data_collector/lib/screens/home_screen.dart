import 'package:flutter/material.dart';
import '../services/timetable_service.dart';
import '../models/timetable.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<TimetableClass> _classes = [];
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'All';

  @override
  void initState() {
    super.initState();
    _loadTimetableData();
  }

  Future<void> _loadTimetableData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final classesData = await TimetableService.getAllClasses();
      final classes =
          classesData.map((data) => TimetableClass.fromJson(data)).toList();

      setState(() {
        _classes = classes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<TimetableClass> _getFilteredClasses() {
    if (_selectedFilter == 'All') {
      return _classes;
    }
    return _classes
        .where((classItem) => classItem.day == _selectedFilter)
        .toList();
  }

  Color _getSubjectColor(String code) {
    if (code.startsWith('CS') || code.startsWith('cs')) {
      return Colors.blue[700]!;
    } else if (code.startsWith('MATH') || code.startsWith('math')) {
      return Colors.red[600]!;
    } else if (code.startsWith('ENG') || code.startsWith('eng')) {
      return Colors.purple[600]!;
    } else if (code.startsWith('PHYS') || code.startsWith('phys')) {
      return Colors.orange[600]!;
    } else if (code.startsWith('CHEM') || code.startsWith('chem')) {
      return Colors.green[600]!;
    } else if (code.startsWith('BIO') || code.startsWith('bio')) {
      return Colors.teal[600]!;
    } else {
      return Colors.grey[700]!;
    }
  }

  void _showAddClassDialog() {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final codeController = TextEditingController();
    final locationController = TextEditingController();
    final professorController = TextEditingController();
    String selectedDay = 'Monday';
    TimeOfDay startTime = const TimeOfDay(hour: 9, minute: 0);
    TimeOfDay endTime = const TimeOfDay(hour: 10, minute: 30);
    bool reminders = false;

    String formatTimeOfDay(TimeOfDay time) {
      final hour = time.hour.toString().padLeft(2, '0');
      final minute = time.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add New Class'),
            content: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Class Name',
                      ),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    TextFormField(
                      controller: codeController,
                      decoration: const InputDecoration(
                        labelText: 'Class Code',
                      ),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    DropdownButtonFormField<String>(
                      value: selectedDay,
                      decoration: const InputDecoration(labelText: 'Day'),
                      items:
                          [
                                'Monday',
                                'Tuesday',
                                'Wednesday',
                                'Thursday',
                                'Friday',
                                'Saturday',
                                'Sunday',
                              ]
                              .map(
                                (day) => DropdownMenuItem(
                                  value: day,
                                  child: Text(day),
                                ),
                              )
                              .toList(),
                      onChanged: (value) {
                        selectedDay = value!;
                      },
                    ),
                    ListTile(
                      title: const Text('Start Time'),
                      subtitle: Text(formatTimeOfDay(startTime)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: startTime,
                        );
                        if (time != null) {
                          setState(() {
                            startTime = time;
                          });
                        }
                      },
                    ),
                    ListTile(
                      title: const Text('End Time'),
                      subtitle: Text(formatTimeOfDay(endTime)),
                      trailing: const Icon(Icons.access_time),
                      onTap: () async {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: endTime,
                        );
                        if (time != null) {
                          setState(() {
                            endTime = time;
                          });
                        }
                      },
                    ),
                    TextFormField(
                      controller: locationController,
                      decoration: const InputDecoration(labelText: 'Location'),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    TextFormField(
                      controller: professorController,
                      decoration: const InputDecoration(labelText: 'Professor'),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    SwitchListTile(
                      title: const Text('Enable Reminders'),
                      value: reminders,
                      onChanged: (value) {
                        setState(() {
                          reminders = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    final newClass = {
                      'name': nameController.text,
                      'code': codeController.text,
                      'day': selectedDay,
                      'startTime': formatTimeOfDay(startTime),
                      'endTime': formatTimeOfDay(endTime),
                      'location': locationController.text,
                      'professor': professorController.text,
                      'reminders': reminders,
                    };

                    try {
                      await TimetableService.addClass(newClass);
                      Navigator.pop(context);
                      _loadTimetableData(); // Reload data
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Class added successfully'),
                        ),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error adding class: $e')),
                      );
                    }
                  }
                },
                child: const Text('ADD'),
              ),
            ],
          ),
    );
  }

  void _showEditClassDialog(TimetableClass classItem) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: classItem.name);
    final codeController = TextEditingController(text: classItem.code);
    final locationController = TextEditingController(text: classItem.location);
    final professorController = TextEditingController(
      text: classItem.professor,
    );
    String selectedDay = classItem.day;

    // Parse existing times
    TimeOfDay startTime = TimeOfDay(
      hour: int.parse(classItem.startTime.split(':')[0]),
      minute: int.parse(classItem.startTime.split(':')[1]),
    );
    TimeOfDay endTime = TimeOfDay(
      hour: int.parse(classItem.endTime.split(':')[0]),
      minute: int.parse(classItem.endTime.split(':')[1]),
    );
    bool reminders = classItem.reminders;

    String formatTimeOfDay(TimeOfDay time) {
      final hour = time.hour.toString().padLeft(2, '0');
      final minute = time.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Edit Class'),
            content: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Class Name',
                      ),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    TextFormField(
                      controller: codeController,
                      decoration: const InputDecoration(
                        labelText: 'Class Code',
                      ),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    DropdownButtonFormField<String>(
                      value: selectedDay,
                      decoration: const InputDecoration(labelText: 'Day'),
                      items:
                          [
                                'Monday',
                                'Tuesday',
                                'Wednesday',
                                'Thursday',
                                'Friday',
                                'Saturday',
                                'Sunday',
                              ]
                              .map(
                                (day) => DropdownMenuItem(
                                  value: day,
                                  child: Text(day),
                                ),
                              )
                              .toList(),
                      onChanged: (value) {
                        selectedDay = value!;
                      },
                    ),
                    StatefulBuilder(
                      builder:
                          (context, setDialogState) => Column(
                            children: [
                              ListTile(
                                title: const Text('Start Time'),
                                subtitle: Text(formatTimeOfDay(startTime)),
                                trailing: const Icon(Icons.access_time),
                                onTap: () async {
                                  final time = await showTimePicker(
                                    context: context,
                                    initialTime: startTime,
                                  );
                                  if (time != null) {
                                    setDialogState(() {
                                      startTime = time;
                                    });
                                  }
                                },
                              ),
                              ListTile(
                                title: const Text('End Time'),
                                subtitle: Text(formatTimeOfDay(endTime)),
                                trailing: const Icon(Icons.access_time),
                                onTap: () async {
                                  final time = await showTimePicker(
                                    context: context,
                                    initialTime: endTime,
                                  );
                                  if (time != null) {
                                    setDialogState(() {
                                      endTime = time;
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                    ),
                    TextFormField(
                      controller: locationController,
                      decoration: const InputDecoration(labelText: 'Location'),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    TextFormField(
                      controller: professorController,
                      decoration: const InputDecoration(labelText: 'Professor'),
                      validator: (value) => value!.isEmpty ? 'Required' : null,
                    ),
                    StatefulBuilder(
                      builder:
                          (context, setDialogState) => SwitchListTile(
                            title: const Text('Enable Reminders'),
                            value: reminders,
                            onChanged: (value) {
                              setDialogState(() {
                                reminders = value;
                              });
                            },
                          ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    final updatedClass = {
                      'name': nameController.text,
                      'code': codeController.text,
                      'day': selectedDay,
                      'startTime': formatTimeOfDay(startTime),
                      'endTime': formatTimeOfDay(endTime),
                      'location': locationController.text,
                      'professor': professorController.text,
                      'reminders': reminders,
                    };

                    try {
                      await TimetableService.updateClass(
                        classItem.id,
                        updatedClass,
                      );
                      Navigator.pop(context);
                      _loadTimetableData(); // Reload data
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Class updated successfully'),
                        ),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error updating class: $e')),
                      );
                    }
                  }
                },
                child: const Text('UPDATE'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteClass(TimetableClass classItem) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Class'),
            content: Text(
              'Are you sure you want to delete "${classItem.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('DELETE'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      try {
        await TimetableService.deleteClass(classItem.id);
        _loadTimetableData(); // Reload data
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Class deleted successfully')),
        );
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error deleting class: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Timetable Data Collector'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() {
                _selectedFilter = value;
              });
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(value: 'All', child: Text('All Days')),
                  const PopupMenuItem(value: 'Monday', child: Text('Monday')),
                  const PopupMenuItem(value: 'Tuesday', child: Text('Tuesday')),
                  const PopupMenuItem(
                    value: 'Wednesday',
                    child: Text('Wednesday'),
                  ),
                  const PopupMenuItem(
                    value: 'Thursday',
                    child: Text('Thursday'),
                  ),
                  const PopupMenuItem(value: 'Friday', child: Text('Friday')),
                  const PopupMenuItem(
                    value: 'Saturday',
                    child: Text('Saturday'),
                  ),
                  const PopupMenuItem(value: 'Sunday', child: Text('Sunday')),
                ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTimetableData,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading timetable data...'),
                  ],
                ),
              )
              : _error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text('Error: $_error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadTimetableData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _classes.isEmpty
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.schedule, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('No classes found'),
                    Text('Tap the + button to add a class'),
                  ],
                ),
              )
              : Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: Colors.blue[50],
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Showing ${_getFilteredClasses().length} classes${_selectedFilter != 'All' ? ' for $_selectedFilter' : ''}',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadTimetableData,
                      child: ListView.builder(
                        itemCount: _getFilteredClasses().length,
                        itemBuilder: (context, index) {
                          final classItem = _getFilteredClasses()[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: _getSubjectColor(
                                  classItem.code,
                                ),
                                child: Text(
                                  classItem.code.substring(0, 2).toUpperCase(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                              title: Text(
                                classItem.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('${classItem.code} • ${classItem.day}'),
                                  Text(
                                    '${classItem.startTime} - ${classItem.endTime}',
                                  ),
                                  Text(
                                    '${classItem.location} • ${classItem.professor}',
                                  ),
                                ],
                              ),
                              trailing: PopupMenuButton<String>(
                                onSelected: (value) {
                                  switch (value) {
                                    case 'edit':
                                      _showEditClassDialog(classItem);
                                      break;
                                    case 'delete':
                                      _deleteClass(classItem);
                                      break;
                                  }
                                },
                                itemBuilder:
                                    (context) => [
                                      const PopupMenuItem(
                                        value: 'edit',
                                        child: Row(
                                          children: [
                                            Icon(Icons.edit),
                                            SizedBox(width: 8),
                                            Text('Edit'),
                                          ],
                                        ),
                                      ),
                                      const PopupMenuItem(
                                        value: 'delete',
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.delete,
                                              color: Colors.red,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              'Delete',
                                              style: TextStyle(
                                                color: Colors.red,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                              ),
                              isThreeLine: true,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddClassDialog,
        child: const Icon(Icons.add),
      ),
    );
  }
}
