import 'package:flutter/material.dart';
import 'services/navigation_service.dart';
import 'widgets/main_navigation.dart';
import 'screens/home_screen.dart';
import 'screens/maps_screen.dart';
import 'screens/event_screen.dart';
import 'screens/connect_screen.dart';
import 'screens/upskill_screen.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Konnect Data Collector',
      navigatorKey: NavigationService.navigatorKey,
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const MainNavigation(),
      routes: {
        AppRoutes.home: (context) => const HomeScreen(),
        AppRoutes.maps: (context) => const MapsScreen(),
        AppRoutes.event: (context) => const EventScreen(),
        AppRoutes.connect: (context) => const ConnectScreen(),
        AppRoutes.upskill: (context) => const UpskillScreen(),
      },
    );
  }
}
