class TimetableClass {
  final int id;
  final String name;
  final String code;
  final String day;
  final String startTime;
  final String endTime;
  final String location;
  final String professor;
  final bool reminders;

  TimetableClass({
    required this.id,
    required this.name,
    required this.code,
    required this.day,
    required this.startTime,
    required this.endTime,
    required this.location,
    required this.professor,
    required this.reminders,
  });

  factory TimetableClass.fromJson(Map<String, dynamic> json) {
    return TimetableClass(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      code: json['code'] ?? '',
      day: json['day'] ?? '',
      startTime: json['startTime'] ?? json['start_time'] ?? '',
      endTime: json['endTime'] ?? json['end_time'] ?? '',
      location: json['location'] ?? '',
      professor: json['professor'] ?? '',
      reminders: json['reminders'] ?? false,
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'day': day,
      'startTime': startTime,
      'endTime': endTime,
      'location': location,
      'professor': professor,
      'reminders': reminders,
    };
  }
}
